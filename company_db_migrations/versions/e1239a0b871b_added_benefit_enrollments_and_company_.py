"""added benefit_enrollments and company_benefits tables

Revision ID: e1239a0b871b
Revises: 1b18fc5a3815
Create Date: 2025-06-19 15:14:19.201473

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e1239a0b871b'
down_revision: Union[str, None] = '1b18fc5a3815'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('company_benefits',
    sa.Column('benefit_id', sa.UUID(), nullable=False),
    sa.Column('benefit_name', sa.String(length=128), nullable=False),
    sa.Column('benefit_amount', sa.Numeric(precision=18, scale=6), nullable=False),
    sa.Column('employer_contribution_fixed', sa.Numeric(precision=18, scale=6), nullable=True),
    sa.Column('employee_contribution_fixed', sa.Numeric(precision=18, scale=6), nullable=True),
    sa.Column('employer_contribution_percentage', sa.Numeric(precision=18, scale=6), nullable=True),
    sa.Column('employee_contribution_percentage', sa.Numeric(precision=18, scale=6), nullable=True),
    sa.Column('benefit_start_date', sa.DateTime(), nullable=False),
    sa.Column('benefit_end_date', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('benefit_id')
    )
    op.create_table('benefit_enrollments',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('employee_id', sa.UUID(), nullable=False),
    sa.Column('company_benefit_id', sa.UUID(), nullable=False),
    sa.Column('custom_total_amount', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('custom_employee_contribution', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('custom_company_contribution', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('start_date', sa.Date(), nullable=False),
    sa.Column('end_date', sa.Date(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['company_benefit_id'], ['company_benefits.benefit_id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['employee_id'], ['employees.employee_id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('employee_id', 'company_benefit_id', name='unique_benefit_enrollment')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('benefit_enrollments')
    op.drop_table('company_benefits')
    # ### end Alembic commands ###
