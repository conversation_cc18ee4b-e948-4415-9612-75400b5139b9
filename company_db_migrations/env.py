from logging.config import fileConfig
from sqlalchemy import engine_from_config
from sqlalchemy import pool
from alembic import context
from app.helpers.company_helpers import CompanyHelpers
from app.models.company import (
    Employee, Payroll, User, Insurance, FeedBack,
    Deductions, Reimbursements, Departments, LeaveApplication,
    LeaveApproval, Attendance)
from app.models.company_approval_work_flow import ApprovalWorkflow, ApprovalLog
from app.models.company_base import DynamicBase
from app.models.company_salary_advance import  SalaryAdvanceRequest, SalaryAdvanceApproval
from app.models.company_payroll_approval import PayrollApproval
from app.models.company_shifts import Shift
from app.models.company_quickbooks import QuickbooksAuditLogs
from app.models.company_documents import Document
from app.models.company_benefits import CompanyBenefit, BenefitEnrollment
from dotenv import load_dotenv
from flask import current_app
import logging
import os
from app import app
load_dotenv()

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata
target_metadata = DynamicBase.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
   # Push the application context so that SQLAlchemy can query the database
    with app.app_context():
        try:
            # Fetch company database names using the helper
            names = CompanyHelpers.get_database_names()
            print("Company database names: ", names)
        except Exception as e:
            logging.error(f"Error getting company database names: {e}")
            names = []

    pwd = os.getenv('DB_PASSWORD')
    db_user = os.getenv('DB_USER')
    db_host = os.getenv('DB_HOST')

    for name in names:
        url = f'postgresql://{db_user}:{pwd}@{db_host}/{name}'
        context.configure(
            url=url,
            target_metadata=target_metadata,
            literal_binds=True,
            dialect_opts={"paramstyle": "named"},
        )

        with context.begin_transaction():
            context.run_migrations()
        
def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    # Push the application context so that SQLAlchemy can query the database
    with app.app_context():
        try:
            # Fetch company database names using the helper
            names = CompanyHelpers.get_database_names()
            print("Company database names: ", names)
        except Exception as e:
            logging.error(f"Error getting company database names: {e}")
            names = []

    pwd = os.getenv('DB_PASSWORD')
    db_user = os.getenv('DB_USER')
    db_host = os.getenv('DB_HOST')

    for name in names:
        url = f'postgresql://{db_user}:{pwd}@{db_host}/{name}'
        connectable = engine_from_config(
            config.get_section(config.config_ini_section, {}),
            prefix="sqlalchemy.",
            poolclass=pool.NullPool,
                        url=url
        )

        with connectable.connect() as connection:
            context.configure(
                connection=connection, target_metadata=target_metadata
            )

            with context.begin_transaction():
                context.run_migrations()
    

if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
