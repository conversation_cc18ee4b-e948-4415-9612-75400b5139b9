#test the sending of emails 

from app.helpers.auxillary import Auxillary
from dotenv import load_dotenv
load_dotenv()

subject = "Test Email"
recipients = ["<EMAIL>","<EMAIL>","muh<PERSON><PERSON><PERSON>@gmail.com"]
body = "This is a test email noreply to see how it sends emails."

try:
    sent = Auxillary.send_netpipo_email(subject, recipients, body)
    print("Email sent successfully!")
    print(sent)
except Exception as e:
    print(f"Error sending email: {e}")