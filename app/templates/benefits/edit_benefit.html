<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <a class ="template-link btn-edit" href="{{ url_for('benefits.benefits') }}">
                    <i class="fi fi-rr-list"></i> Benefits
                </a>
                <a class ="template-link btn-edit" href="{{ url_for('benefits.view_benefit', benefit_id=benefit.benefit_id) }}">
                    <i class="fi fi-rr-eye"></i> View Benefit
                </a>
            </div>
        </div>
        <div class="dyn_container">
            <div class="form--container">
                <h1>Edit Benefit: {{ benefit.benefit_name }}</h1>
                <form method="POST" action="{{ url_for('benefits.update_benefit', benefit_id=benefit.benefit_id) }}">
                    {{ form.hidden_tag() }}
                    <div class="form-row">
                        <div class="form-group">
                            {{ form.benefit_name.label }}
                            <div class="input-group-text">
                                {{ form.benefit_name(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            {{ form.benefit_amount.label }}
                            <div class="input-group-text">
                                {{ form.benefit_amount(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            {{ form.employer_contribution_fixed.label }}
                            <div class="input-group-text">
                                {{ form.employer_contribution_fixed(class="form-control") }}
                            </div>
                            <small class="form-text text-muted">Leave empty if using percentage</small>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            {{ form.employee_contribution_fixed.label }}
                            <div class="input-group-text">
                                {{ form.employee_contribution_fixed(class="form-control") }}
                            </div>
                            <small class="form-text text-muted">Leave empty if using percentage</small>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            {{ form.employer_contribution_percentage.label }}
                            <div class="input-group-text">
                                {{ form.employer_contribution_percentage(class="form-control") }}
                            </div>
                            <small class="form-text text-muted">Leave empty if using fixed amount</small>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            {{ form.employee_contribution_percentage.label }}
                            <div class="input-group-text">
                                {{ form.employee_contribution_percentage(class="form-control") }}
                            </div>
                            <small class="form-text text-muted">Leave empty if using fixed amount</small>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            {{ form.benefit_start_date.label }}
                            <div class="input-group-text">
                                {{ form.benefit_start_date(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            {{ form.benefit_end_date.label }}
                            <div class="input-group-text">
                                {{ form.benefit_end_date(class="form-control") }}
                            </div>
                            <small class="form-text text-muted">Leave empty for ongoing benefit</small>
                        </div>
                    </div>
                    <div class="form-buttons">
                        {{ form.submit(class="primary-button") }}
                        <a href="{{ url_for('benefits.benefits') }}" class="secondary-button">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
{% endblock %}
