<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <a class ="template-link btn-edit" href="{{ url_for('benefits.benefits') }}">
                    <i class="fi fi-rr-list"></i> Benefits
                </a>
                <a class ="template-link btn-edit" href="{{ url_for('benefits.view_benefit', benefit_id=benefit.benefit_id) }}">
                    <i class="fi fi-rr-eye"></i> View Benefit
                </a>
            </div>
        </div>
        <div class="dyn_container">
            <div class="form--container">
                <h1>Delete Benefit</h1>
                <div class="alert alert-warning">
                    <i class="fi fi-rr-triangle-warning"></i>
                    <strong>Warning!</strong> This action cannot be undone. Deleting this benefit will also remove all employee enrollments associated with it.
                </div>
                
                <div class="benefit-details">
                    <h3>Benefit Details</h3>
                    <table class="table table-bordered">
                        <tr>
                            <th>Benefit Name</th>
                            <td>{{ benefit.benefit_name }}</td>
                        </tr>
                        <tr>
                            <th>Benefit Amount</th>
                            <td>{{ benefit.benefit_amount }}</td>
                        </tr>
                        {% if benefit.employer_contribution_fixed %}
                        <tr>
                            <th>Employer Contribution (Fixed)</th>
                            <td>{{ benefit.employer_contribution_fixed }}</td>
                        </tr>
                        {% endif %}
                        {% if benefit.employee_contribution_fixed %}
                        <tr>
                            <th>Employee Contribution (Fixed)</th>
                            <td>{{ benefit.employee_contribution_fixed }}</td>
                        </tr>
                        {% endif %}
                        {% if benefit.employer_contribution_percentage %}
                        <tr>
                            <th>Employer Contribution (%)</th>
                            <td>{{ benefit.employer_contribution_percentage }}%</td>
                        </tr>
                        {% endif %}
                        {% if benefit.employee_contribution_percentage %}
                        <tr>
                            <th>Employee Contribution (%)</th>
                            <td>{{ benefit.employee_contribution_percentage }}%</td>
                        </tr>
                        {% endif %}
                        <tr>
                            <th>Start Date</th>
                            <td>{{ benefit.benefit_start_date }}</td>
                        </tr>
                        {% if benefit.benefit_end_date %}
                        <tr>
                            <th>End Date</th>
                            <td>{{ benefit.benefit_end_date }}</td>
                        </tr>
                        {% endif %}
                        <tr>
                            <th>Created At</th>
                            <td>{{ benefit.created_at }}</td>
                        </tr>
                    </table>
                </div>

                <form method="POST" action="{{ url_for('benefits.delete_benefit', benefit_id=benefit.benefit_id) }}">
                    <div class="form-buttons">
                        <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this benefit? This action cannot be undone.')">
                            <i class="fi fi-rr-trash"></i> Delete Benefit
                        </button>
                        <a href="{{ url_for('benefits.benefits') }}" class="secondary-button">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
{% endblock %}
