<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <a class="btn-edit template-link" href="#" data-template-url="{{ url_for('benefits.add_benefit') }}">
                    <i class="fi fi-rr-plus-small"></i> Add Benefit
                </a>
            </div>
        </div>
        <div class="dyn_container">
            <div class="large--table">
                <table class="table">
                    <thead class="thead th-custom">
                        <tr>
                            <th>Benefit Name</th>
                            <th>Benefit Amount</th>
                            <th>Employer Contribution Fixed</th>
                            <th>Employee Contribution Fixed</th>
                            <th>Employer Contribution Percentage</th>
                            <th>Employee Contribution Percentage</th>
                            <th>Benefit Start Date</th>
                            <th>Benefit End Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for benefit in benefits %}
                        <tr>
                            <td>{{ benefit.benefit_name }}</td>
                            <td>{{ benefit.benefit_amount }}</td>
                            <td>{{ benefit.employer_contribution_fixed }}</td>
                            <td>{{ benefit.employee_contribution_fixed }}</td>
                            <td>{{ benefit.employer_contribution_percentage }}</td>
                            <td>{{ benefit.employee_contribution_percentage }}</td>
                            <td>{{ benefit.benefit_start_date }}</td>
                            <td>{{ benefit.benefit_end_date }}</td>
                            <td>
                                <div class="table-buttons">
                                    <a class="template-link btn-edit" href="#" data-template-url="{{ url_for('benefits.update_benefit', benefit_id=benefit.benefit_id) }}">
                                        <i class="fi fi-rr-pencil"></i>
                                        <p>Edit</p>
                                    </a>
                                    <a class="btn-cancel template-link" href="#" data-template-url="{{ url_for('benefits.delete_benefit', benefit_id=benefit.benefit_id) }}">
                                        <i class="fi fi-rr-trash"></i>
                                        <p>Delete</p>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% endblock %}