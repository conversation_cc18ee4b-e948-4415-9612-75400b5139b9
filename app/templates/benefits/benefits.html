<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <a class="btn-edit" href="{{ url_for('benefits.add_benefit') }}">
                    <i class="fi fi-rr-plus-small"></i> Add Benefit
                </a>
            </div>
        </div>
        <div class="dyn_container">
            <div class="large--table">
                <table id="benefitsTable" class="table table-striped table-bordered">
                    <thead class="thead th-custom">
                        <tr>
                            <th>Benefit Name</th>
                            <th>Total Amount</th>
                            <th>Employer Contribution</th>
                            <th>Employee Contribution</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for benefit in benefits %}
                        <tr>
                            <td>
                                <a href="{{ url_for('benefits.view_benefit', benefit_id=benefit.benefit_id) }}" class="benefit-link">
                                    {{ benefit.benefit_name }}
                                </a>
                            </td>
                            <td>{{ benefit.benefit_amount }}</td>
                            <td>
                                {% if benefit.employer_contribution_fixed %}
                                    {{ benefit.employer_contribution_fixed }} (Fixed)
                                {% elif benefit.employer_contribution_percentage %}
                                    {{ benefit.employer_contribution_percentage }}% ({{ benefit.employer_contribution or 'N/A' }})
                                {% else %}
                                    N/A
                                {% endif %}
                            </td>
                            <td>
                                {% if benefit.employee_contribution_fixed %}
                                    {{ benefit.employee_contribution_fixed }} (Fixed)
                                {% elif benefit.employee_contribution_percentage %}
                                    {{ benefit.employee_contribution_percentage }}% ({{ benefit.employee_contribution or 'N/A' }})
                                {% else %}
                                    N/A
                                {% endif %}
                            </td>
                            <td>{{ benefit.benefit_start_date }}</td>
                            <td>
                                {% if benefit.benefit_end_date %}
                                    {{ benefit.benefit_end_date }}
                                {% else %}
                                    <span class="badge badge-success">Ongoing</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if benefit.benefit_end_date %}
                                    <span class="badge badge-secondary">Ended</span>
                                {% else %}
                                    <span class="badge badge-success">Active</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="table-buttons">
                                    <a class="btn-info" href="{{ url_for('benefits.view_benefit', benefit_id=benefit.benefit_id) }}" title="View">
                                        <i class="fi fi-rr-eye"></i>
                                    </a>
                                    <a class="btn-edit" href="{{ url_for('benefits.update_benefit', benefit_id=benefit.benefit_id) }}" title="Edit">
                                        <i class="fi fi-rr-pencil"></i>
                                    </a>
                                    <a class="btn-cancel" href="{{ url_for('benefits.delete_benefit', benefit_id=benefit.benefit_id) }}" title="Delete">
                                        <i class="fi fi-rr-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            $('#benefitsTable').DataTable({
                "pageLength": 25,
                "order": [[ 0, "asc" ]],
                "responsive": true,
                "dom": '<"top"f>rt<"bottom"lip><"clear">',
                "language": {
                    "search": "Search benefits:",
                    "lengthMenu": "Show _MENU_ benefits per page",
                    "info": "Showing _START_ to _END_ of _TOTAL_ benefits",
                    "infoEmpty": "No benefits found",
                    "infoFiltered": "(filtered from _MAX_ total benefits)",
                    "zeroRecords": "No matching benefits found"
                }
            });
        });
    </script>
{% endblock %}