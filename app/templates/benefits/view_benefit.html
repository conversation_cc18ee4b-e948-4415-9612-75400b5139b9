<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <a class ="template-link btn-edit" href="{{ url_for('benefits.benefits') }}">
                    <i class="fi fi-rr-list"></i> Benefits
                </a>
                <a class ="template-link btn-edit" href="{{ url_for('benefits.update_benefit', benefit_id=benefit.benefit_id) }}">
                    <i class="fi fi-rr-pencil"></i> Edit Benefit
                </a>
                <a class ="template-link btn-cancel" href="{{ url_for('benefits.delete_benefit', benefit_id=benefit.benefit_id) }}">
                    <i class="fi fi-rr-trash"></i> Delete Benefit
                </a>
            </div>
        </div>
        <div class="dyn_container">
            <div class="form--container">
                <h1>{{ benefit.benefit_name }}</h1>
                
                <div class="benefit-overview">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-card">
                                <h3>Benefit Information</h3>
                                <table class="table table-borderless">
                                    <tr>
                                        <th>Benefit Name:</th>
                                        <td>{{ benefit.benefit_name }}</td>
                                    </tr>
                                    <tr>
                                        <th>Total Amount:</th>
                                        <td>{{ benefit.benefit_amount }}</td>
                                    </tr>
                                    <tr>
                                        <th>Start Date:</th>
                                        <td>{{ benefit.benefit_start_date }}</td>
                                    </tr>
                                    {% if benefit.benefit_end_date %}
                                    <tr>
                                        <th>End Date:</th>
                                        <td>{{ benefit.benefit_end_date }}</td>
                                    </tr>
                                    {% else %}
                                    <tr>
                                        <th>Status:</th>
                                        <td><span class="badge badge-success">Ongoing</span></td>
                                    </tr>
                                    {% endif %}
                                </table>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-card">
                                <h3>Contribution Details</h3>
                                <table class="table table-borderless">
                                    {% if benefit.employer_contribution_fixed %}
                                    <tr>
                                        <th>Employer Contribution (Fixed):</th>
                                        <td>{{ benefit.employer_contribution_fixed }}</td>
                                    </tr>
                                    {% endif %}
                                    {% if benefit.employer_contribution_percentage %}
                                    <tr>
                                        <th>Employer Contribution (%):</th>
                                        <td>{{ benefit.employer_contribution_percentage }}%</td>
                                    </tr>
                                    {% endif %}
                                    {% if benefit.employee_contribution_fixed %}
                                    <tr>
                                        <th>Employee Contribution (Fixed):</th>
                                        <td>{{ benefit.employee_contribution_fixed }}</td>
                                    </tr>
                                    {% endif %}
                                    {% if benefit.employee_contribution_percentage %}
                                    <tr>
                                        <th>Employee Contribution (%):</th>
                                        <td>{{ benefit.employee_contribution_percentage }}%</td>
                                    </tr>
                                    {% endif %}
                                    <tr>
                                        <th>Calculated Employer Contribution:</th>
                                        <td>{{ benefit.employer_contribution or 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Calculated Employee Contribution:</th>
                                        <td>{{ benefit.employee_contribution or 'N/A' }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="enrollment-stats">
                    <h3>Enrollment Statistics</h3>
                    <div class="stats-cards">
                        <div class="stat-card">
                            <div class="stat-number">{{ enrollment_count }}</div>
                            <div class="stat-label">Total Enrollments</div>
                        </div>
                    </div>
                </div>

                {% if enrollments %}
                <div class="enrollments-section">
                    <h3>Current Enrollments</h3>
                    <div class="large--table">
                        <table class="table">
                            <thead class="thead th-custom">
                                <tr>
                                    <th>Employee ID</th>
                                    <th>Custom Total Amount</th>
                                    <th>Custom Employee Contribution</th>
                                    <th>Custom Company Contribution</th>
                                    <th>Start Date</th>
                                    <th>End Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for enrollment in enrollments %}
                                <tr>
                                    <td>{{ enrollment.employee_id }}</td>
                                    <td>{{ enrollment.custom_total_amount or 'Default' }}</td>
                                    <td>{{ enrollment.custom_employee_contribution or 'Default' }}</td>
                                    <td>{{ enrollment.custom_company_contribution or 'Default' }}</td>
                                    <td>{{ enrollment.start_date }}</td>
                                    <td>{{ enrollment.end_date or 'Ongoing' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% else %}
                <div class="no-enrollments">
                    <p class="text-muted">No employees are currently enrolled in this benefit.</p>
                </div>
                {% endif %}

                <div class="audit-info">
                    <h3>Audit Information</h3>
                    <table class="table table-borderless">
                        <tr>
                            <th>Created At:</th>
                            <td>{{ benefit.created_at }}</td>
                        </tr>
                        <tr>
                            <th>Last Updated:</th>
                            <td>{{ benefit.updated_at }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
