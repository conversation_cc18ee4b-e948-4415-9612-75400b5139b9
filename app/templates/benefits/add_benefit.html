<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <a class ="template-link btn-edit" href="{{ url_for('benefits.benefits') }}">
                    <i class="fi fi-rr-list"></i> Benefits
                </a>
            </div>
        </div>
        <div class="dyn_container">
            <div class="form--container">
                <h1>Add Benefit</h1>
                <form method="POST" action="{{ url_for('benefits.add_benefit') }}">
                    {{ form.hidden_tag() }}
                    <div class="form-row">
                        <div class="form-group">
                            {{ form.benefit_name.label }}
                            <div class="input-group-text">
                                {{ form.benefit_name(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            {{ form.benefit_amount.label }}
                            <div class="input-group-text">
                                {{ form.benefit_amount(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            {{ form.employer_contribution_fixed.label }}
                            <div class="input-group-text">
                                {{ form.employer_contribution_fixed(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            {{ form.employee_contribution_fixed.label }}
                            <div class="input-group-text">
                                {{ form.employee_contribution_fixed(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            {{ form.employer_contribution_percentage.label }}
                            <div class="input-group-text">
                                {{ form.employer_contribution_percentage(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            {{ form.employee_contribution_percentage.label }}
                            <div class="input-group-text">
                                {{ form.employee_contribution_percentage(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            {{ form.benefit_start_date.label }}
                            <div class="input-group-text">
                                {{ form.benefit_start_date(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            {{ form.benefit_end_date.label }}
                            <div class="input-group-text">
                                {{ form.benefit_end_date(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    {{ form.submit(class="primary-button") }}
                </form>
            </div>
        </div>
    </div>
{% endblock %}
