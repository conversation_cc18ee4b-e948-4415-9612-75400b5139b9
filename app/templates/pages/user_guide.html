<!DOCTYPE html>
{% extends 'layouts/home_.html' %}
{% block content %}
<div class="flex h-screen"  style= "background: rgba(255, 255, 255, 0.5);">
    <aside class="border border-r-2 border-gray-200 text-gray-500 py-6 px-3 hidden sm:block  overflow-y-auto" style= "background: rgba(255, 255, 255, 0.6)">
        <h2 class="text-2xl font-bold mb-4 dark border-b-2 border-gray-200 w-full">User Guide</h2>
        <nav class="space-y-2">
            <a href="#chapter1" class="block hover:bg-green-100 py-1 px-2 rounded-lg focus:ring-2 focus-within:ring-green-200">Chapter 1: Introduction</a>
            <a href="#chapter2" class="block hover:bg-green-100 py-1 px-2 rounded-lg ">Chapter 2: Key Features</a>
            <a href="#chapter3" class="block hover:bg-green-100 py-1 px-2 rounded-lg ">Chapter 3: Getting Started</a>
            <a href="#chapter4" class="block hover:bg-green-100 py-1 px-2 rounded-lg ">Chapter 4: Company Registration</a>
            <a href="#chapter5" class="block hover:bg-green-100 py-1 px-2 rounded-lg ">Chapter 5: Dashboard Overview</a>
            <a href="#chapter6" class="block hover:bg-green-100 py-1 px-2 rounded-lg ">Chapter 6: Managing Settings</a>
            <a href="#chapter7" class="block hover:bg-green-100 py-1 px-2 rounded-lg ">Chapter 7: Employee registration</a>
            <a href="#chapter8" class="block hover:bg-green-100 py-1 px-2 rounded-lg ">Chapter 8: Payroll Processing</a>
            <a href="#chapter9" class="block hover:bg-green-100 py-1 px-2 rounded-lg ">Chapter 9: Exporting Annexures</a>
            <a href="#chapter10" class="block hover:bg-green-100 py-1 px-2 rounded-lg ">Chapter 10: Reimbursements</a>
            <a href="#chapter11" class="block hover:bg-green-100 py-1 px-2 rounded-lg ">Chapter 11: Deductions</a>
            <a href="#chapter12" class="block hover:bg-green-100 py-1 px-2 rounded-lg ">Chapter 12: Medical Insurance</a>
            <a href="#chapter13" class="block hover:bg-green-100 py-1 px-2 rounded-lg ">Chapter 13: Departments</a>
            <a href="#chapter14" class="block hover:bg-green-100 py-1 px-2 rounded-lg ">Chapter 14: Attendance Management</a>
            <a href="#chapter15" class="block hover:bg-green-100 py-1 px-2 rounded-lg ">Chapter 15: Leave Management</a>
            <a href="#chapter16" class="block hover:bg-green-100 py-1 px-2 rounded-lg ">Chapter 16: Multiple Companies</a>
            <a href="#chapter17" class="block hover:bg-green-100 py-1 px-2 rounded-lg ">Chapter 17: Support</a>
        </nav>
    </aside>
    <!-- Main Content -->
    <div class="flex-1 p-8 overflow-y-auto">
        <!-- Chapter 1 -->
        <section id="chapter1" class="mb-12">
            <h2 class="dark">Chapter 1: Introduction</h2>
            <p class="mt-2 mid-dark">
                NETPIPO is a cloud-based HR and payroll management system that helps businesses of all sizes in Rwanda automate their payroll processes, ensure compliance with tax regulations, and simplify HR management. Our innovative platform is designed to streamline payroll operations, reduce errors, and save time, allowing businesses to focus on growth and success.
            </p>
        </section>
        
        <!-- Chapter 2 -->
        <section id="chapter2" class="mb-12">
            <h2 class="dark">Chapter 2: Key Features</h2>
            <p class="mt-2 mid-dark">
                NETPIPO offers a range of features to streamline your HR and payroll processes:
            </p>
            <ul class="list-disc list-inside mt-2 mid-dark">
                <li>Employees Management</li>
                <li>Auto-generate Payroll lists</li>
                <li>Payroll Tax Calculations</li>
                <li>Monthly Payroll Tax Summary report</li>
                <li>Automatic RRA PAYE Annexure generation</li>
                <li>RSSB: Pension, Maternity, RAMA, CBHI Annexures</li>
                <li>Other deductions (Advances and Installments)</li>
                <li>Reimbursements Management</li>
                <li>Medical insurance Management</li>
            </ul>
        </section>
        
        <!-- Chapter 3 -->
        <section id="chapter3" class="mb-12">
            <h2 class="dark">Chapter 3: Getting Started</h2>
            <p class="mt-2 mid-dark">
                To get started with NETPIPO, you need to sign up for an account. Follow these steps to create an account:
            </p>
            <ol class="list-decimal list-inside mt-2 mid-dark">
                <li>Visit the NETPIPO website at <a href="https://www.netpipo.com" class="text-blue-600 hover:underline">www.netpipo.com</a></li>
                <li>Navigate to the sign-up page</li>
                <li>Fill in the required information, including your name, email address, phone number, and password</li>
                <li>Click on the "Register" button to create your account</li>
                <li>Check your email for a verification link and click on it to activate your account</li>
            </ol>
        </section>
        
        <!-- Chapter 4 -->
        <section id="chapter4" class="mb-12">
            <h2 class="dark">Chapter 4: Company Registration</h2>
            <p class="mt-2 mid-dark">
                Before you can start using NETPIPO to manage your payroll and HR processes, you need to register your company on the platform. Follow these steps to register your company:
            </p>
            <ol class="list-decimal list-inside mt-2 mid-dark">
                <li>After verifying your OTP code, you will be redirected to register your company if you do not have one created</li>
                <li>Fill in the required information, including your company name, address, and tax identification number</li>
                <li>Click on the "Register" button to complete the registration process</li>
            </ol>
        </section>
        
        <!-- Chapter 5 -->
        <section id="chapter5" class="mb-12">
            <h2 class="dark">Chapter 5: Dashboard Overview</h2>
            <p class="mt-2 mid-dark">
                Once you have logged in to your NETPIPO account and registered your company, you will be taken to the dashboard, where you can access all the features and functionalities of the platform. The dashboard provides an overview of your payroll processes, employee data, and tax compliance status, allowing you to manage your HR and payroll operations efficiently.
            </p>
            <p class="mt-2 mid-dark">
                The dashboard displays key information such as:
            </p>
            <ul class="list-disc list-inside mt-2 mid-dark">
                <li>Number of Employees</li>
                <li>Payroll Expense</li>
                <li>Payroll &amp; Contributions</li>
                <li>Net Salaries</li>
                <li>Gross salaries</li>
                <li>Total staff cost</li>
            </ul>
        </section>
        <section id="chapter6" class="mb-12">
            <h2 class="dark">Chapter 6: Manage Settings</h2>
            <p>All the information that was saved during sign up, ad registering company are displayed in the settings panel</p>
            <ol class="list-decimal list-inside mid-dark">
                <li>Navigate to the sidebar</li>
                <li>Click on the settings icon</li>
                <li>Update your information including company details, upload company's logo, create/manage company users</li>
                <ol class="list-decimal list-inside mt-4 mid-dark ml-6">
                    <h3 class="text-xl font-semibold mid-dark border-b-2 border-gray-300 p-2 w-1/2">Create/manage Company Users</h3>
                    <p class="mt-2 mid-dark">
                        You can create and manage users who will have access to the NETPIPO platform (HRs, Accountants). Follow these steps to create a new user:
                    </p>
                    <ol class="list-decimal list-inside mt-2 mid-dark">
                        <li>Navigate to the settings</li>
                        <li>Click on the "Users"</li>
                        <li>Click on the "Add User" button</li>
                        <li>Fill in the required information, including the user's name, email address, and role</li>
                        <li>Click on the "Add User" button to create the user account</li>
                    </ol>
                </ol>
                <ol class="list-decimal list-inside mt-4 mid-dark ml-6">
                    <h3 class="text-xl font-semibold mid-dark border-b-2 border-gray-300 p-2 w-1/2">Other users</h3>
                    <p class="mt-2 mid-dark">
                    NETPIPO allows you to create other users who will have access to the platform through their own dashboards. Follow these steps to create other users (Supervisors and Employees):
                    </p>
                    <ol class="list-decimal list-inside mt-2 mid-dark">
                        <li>Navigate to the settings</li>
                        <li>Under Attendance Management Section</li>
                        <li>Click on the "Users" button that will redirect you to the page where you can view/edit/delete the existing users or create new users</li>
                        <li>Click on create new user button</li>
                        <li>Fill in the required information, including the user's name, email address, and role</li>
                    </ol>
                </ol>
                <ol class="list-decimal list-inside mt-4 mid-dark ml-6">
                    <h3 class="text-xl font-semibold mid-dark border-b-2 border-gray-300 p-2 w-1/2">Approval Workflows</h3>
                    <p class="mt-2 mid-dark">
                        NETPIPO allows you to set up an approval workflow for payroll processing and leave management. Follow these steps to set up an approval workflow:
                    </p>
                    <ol class="list-decimal list-inside mt-2 mid-dark">
                        <li>Navigate to the settings</li>
                        <li>Under the Approval Workflow Section</li>
                        <li>Click on the "Add Approval Workflow" button</li>
                        <li>Fill in the required information, including the approver's name, and role</li>
                        <li>Click on the "Add Approval Workflow" button to set up the approval workflow</li>
                    </ol>
                </ol>
            </ol>
        </section>
        <section id="chapter7">
            <h2 class="dark">Chapter 7: Employee registration</h2>
            <p class="mt-2 mid-dark">
                To add employees to the NETPIPO platform, follow these steps:
            </p>
            <ol class="list-decimal list-inside mt-4 mid-dark ml-6">
                <h3 class="text-xl font-semibold mid-dark border-b-2 border-gray-300 p-2 w-1/2">Single employee registration</h3>
                <p class="mt-2 mid-dark">
                    You can add employees one by one to the NETPIPO. Follow these steps to add a single employee:
                </p>                
                <ol class="list-decimal list-inside mt-2 mid-dark">
                    <li>Click on the "Employees" from the dashboard sidebar</li>
                    <li>Click on the "Add Employee" button</li>
                    <li>Fill in the required information, including the employee's name, email address, phone number, and job title</li>
                    <li>Click on the "Register Employee" button to add the employee to the system</li>
                </ol>
            </ol>
        </section>
        <section id="chapter8">
            <h2 class="dark">Chapter 8: Payroll Processing</h2>
            <p class="mt-2 mid-dark">
                Once you have registered employees on the platform, you can start processing payroll. Follow these steps to process payroll:
            </p>
            <ol class="list-decimal list-inside mt-4 mid-dark ml-6">
                <li>Click on the "Payroll" tab on the dashboard</li>
                <li>Click on the "Process Payroll" button</li>
                <li>Click on the "Payroll button"</li>
                <li>Select the last date of the month that you want to process the payroll for</li>
                <li>If your plan supports attendance management you will have to select if time attendance shall affect the payroll</li>
                <li>Click on the "Generate" button to generate the payroll list</li>
            </ol>
        </section>
        <section id="chapter9">
            <h2 class="dark">Chapter 9: Exporting Annexures</h2>
            <p class="mt-2 mid-dark">
                NETPIPO allows you to generate and export/download annexures for tax compliance and reporting purposes. Follow these steps to export annexures
            </p>
            <ol class="list-decimal list-inside mt-4 mid-dark ml-6">
                <li>Click on the "Export" while you are on the payroll summary panel</li>
                <li>Select the type of annexure you want to export, such as RRA PAYE Annexure or RSSB Annexure</li>
                <li>make sure that the pay period and the employees you want to include in the annexure are correctly recorded</li>
                <li>Click on the "Export Annexure" button to generate and download the annexure</li>
            </ol> 
            <p class="italic m-4 text-gray-500">for more activities like add payroll or save completed payroll, downloading Unified permanent Employees annexure, payroll summary and all Employees payslips</p>
            <ol class="list-decimal ml-4 italic text-gray-500">
                <li>Click on the "More" button</li>
                <li>Select the activity you want to perform</li>
            </ol>
        </section>
        <section id="chapter10">
            <h2 class="dark">10. Reimbursements</h2>
            <p class="mt-2 mid-dark">
                NETPIPO allows you to manage employee reimbursements for expenses incurred during work. Follow these steps to manage reimbursements:
            </p>
            <ol class="list-decimal list-inside mt-4 mid-dark ml-6">
                <li>Click on the "Reimbursements" tab on the dashboard</li>
                <li>Click on the record Reimbursement" button</li>
                <li>Fill in the required information, including the employee's name, the amount of the reimbursement, and the reason for the reimbursement</li>
                <li>Click on the "Add Reimbursement" button to record the reimbursement</li>
            </ol>
        </section>
        <section id="chapter11">
            <h2 class="dark">11. Deductions</h2>
            <p class="mt-2 mid-dark">
                Netpipo was built with the feature to manage the deductions upon net salary (net to pay) and those that can affect the gross salary
            </p>
            <ol class="list-decimal list-inside mt-4 mid-dark ml-6">
                <li>Click on the "Deductions" tab on the dashboard</li>
                <li>Click on the "+ New Deduction" button</li>
                <li>Fill in the required information, including the employee's name, the amount of the deduction, and the reason for the deduction</li>
                <li>Click on the "Add Deduction" button to record the deduction</li>
            </ol>
        </section>
        <section id="chapter12">
            <h2 class="dark">12. Medical Insurance</h2>
            <p class="mt-2 mid-dark">
                if your company pays for employees' insurance NETPIPO allows you to manage employee medical insurance details, including coverage and contribution information. Follow these steps to manage medical insurance:</p>
            <ol class="list-decimal list-inside mt-4 mid-dark ml-6">
                <li>Click on the "Departments" tab on the dashboard</li>
                <li>Click on the "Add Department" button</li>
                <li>Fill in the department name</li>
                <li>Click on the "Add Department" button to create the department</li>
            </ol>
        </section>
        <section id="chapter13">
            <h2 class="dark">Chapter 13: Departments</h2>
            <p class="mt-2 mid-dark">
                NETPIPO allows you to create departments within your company to organize employees and manage payroll processes more efficiently. Follow these steps to create departments:
            </p>
            <ol class="list-decimal list-inside mt-4 mid-dark ml-6">
                <li>Click on the "Departments" tab on the dashboard</li>
                <li>Click on the "Add Department" button</li>
                <li>Fill in the department name</li>
                <li>Click on the "Add Department" button to create the department</li>
            </ol>
        </section>
        <section id="chapter14">
            <h2 class="dark">Chapter 14: Attendance Management</h2>
            <p class="mt-2 mid-dark">
                NETPIPO allows you to track employee attendance and manage leave requests. Follow these steps to manage attendance:
            </p>
            <ol class="list-decimal list-inside mt-4 mid-dark ml-6">
                <h3 class="text-xl font-semibold mid-dark border-b-2 border-gray-300 p-2 w-1/2">1. Create Subject</h3>
                <p class="mt-2 text-gray">NETPIPO allows you to manage employee attendance and track working hours. Follow these steps to manage attendance:</p>
                <li>Make sure that your plan has the feature of attendance.</li>
                <li>First and foremost, you need to have HR or supervisor role.</li>
                <li>In the dashboard, click on employees list, in the action column, click on the image icon to create the employee's image subject.</li>
                <li>You are redirected to the page where you will select to upload the employee's image file, or take a snapshot right away, and save the Image</li>
                <li>Navigate to the sidebar</li>
                <li>Click on the "Attendance" tab</li>
            </ol>
            <ol class="list-decimal list-inside mt-4 mid-dark ml-6">
                <h3 class="text-xl font-semibold mid-dark border-b-2 border-gray-300 p-2 w-1/2">2. Clockin &amp; Clockout</h3>
                <p class="mt-2 text-gray">NETPIPO allows you to manage employee attendance and track working days and hours. Follow these steps to manage attendance:</p>
                <ol class="list-decimal list-inside mt-2 mid-dark">
                    <li>Click on the "Clock in" button to Scan the employee face and click on capture</li>
                    <li>Click on the "Clock out" button to Scan the employee face and click on capture</li>
                    <li>Click on the "Attendance records" tab to view the attendance history</li>
                    <li>Click on Timesheet to view the employee's monthly attendance history</li>
                </ol>
            </ol>
        </section>
        <section id="chapter15">
            <h2 class="dark">Chapter 15: Leave Management</h2>
            <p class="mt-2 mid-dark">
                NETPIPO allows you to manage employee leave requests and track leave balances. Follow these steps to manage leave:
            </p>
            <ol class="list-decimal list-inside mt-4 mid-dark ml-6">
                <h3 class="text-xl font-semibold mid-dark border-b-2 border-gray-300 p-2 w-1/2">1. Human-resources manager role</h3>
                <p class="mt-2 text-gray">NETPIPO allows you to manage employee leave requests and track leave balances. Follow these steps to manage leave:</p>
                <li>Click on the "Leave" tab on the dashboard</li>
                <li>Click on "Leave requests" to view and approve the requested leave applications</li>
                <li>Click on "Leave approvals" to view the approved leave applications</li>
                <li>Click on "Leave records" to view the leave history of employees</li>
            </ol>
            <ol class="list-decimal list-inside mt-4 mid-dark ml-6">
                <h3 class="text-xl font-semibold mid-dark border-b-2 border-gray-300 p-2 w-1/2">2. Supervisor</h3>
                <p class="mt-2 text-gray">NETPIPO allows you to manage employee leave requests Follow these steps to review and approve leave:</p>
                <ol class="list-decimal list-inside mt-2 mid-dark">
                    <li>Click on the "Leave" tab on the dashboard</li>
                    <li>Click on "Leave requests" to view and approve the requested leave applications</li>
                    <li>Click on "Leave approvals" to view the approved leave applications</li>
                    <li>Click on "Leave records" to view the leave history of employees</li>
                </ol>
            </ol>
            <ol class="list-decimal list-inside mt-4 mid-dark ml-6">
                <h3 class="text-xl font-semibold mid-dark border-b-2 border-gray-300 p-2 w-1/2">3. Employee</h3>
                <p class="mt-2 text-gray">NETPIPO allows employees to request leave</p>
                <ol class="list-decimal list-inside mt-2 mid-dark">
                    <li>Click on the "Leave" tab on the dashboard</li>
                    <li>Click on "Request leave" to request leave</li>
                    <li>Fill in the required information, including the leave type, start date, end date, and reason for the leave</li>
                    <li>Click on the "Submit" button to submit the leave request</li>
                </ol>
            </ol>
        </section>
        <section id="chapter16">
            <h2 class="dark">Chapter 16: Salary Advances</h2>
            <p class="mt-2 mid-dark">
                NETPIPO allows Employees to request salary advances, and HRs to edit, approve or reject the requested salary advances. Follow these steps to manage salary advances:
            </p>
            <ol class="list-decimal list-inside mt-4 mid-dark ml-6">
                <h3 class="text-xl font-semibold mid-dark border-b-2 border-gray-300 p-2 w-1/2">1. Employee</h3>
                <p class="mt-2 text-gray">NETPIPO allows employees to request salary advances</p>
                <ol class="list-decimal list-inside mt-2 mid-dark">
                    <li>Click on the "Salary Advances" tab on the dashboard</li>
                    <li>Click on "Request Salary Advance" to request a salary advance</li>
                    <li>Fill in the required information, including the amount of the advance and the Installments to pay back</li>
                    <li>Click on the "Submit" button to submit the salary advance request</li>
                </ol>
            </ol>
            <ol class="list-decimal list-inside mt-4 mid-dark ml-6">
                <h3 class="text-xl font-semibold mid-dark border-b-2 border-gray-300 p-2 w-1/2">2. HR</h3>
                <p class="mt-2 text-gray">NETPIPO allows HRs to manage salary advances</p>
                <ol class="list-decimal list-inside mt-2 mid-dark">
                    <li>Click on the "Salary Advances" tab on the dashboard</li>
                    <li>Click on "Salary Advances" to view, update, approve or reject the requested salary advances</li>
                    <li>Click on "Salary Advances History" to view the history of salary advances</li>
                    <li>Click on "Salary Advances Installments" to view the history of salary advances installments</li>
                </ol>
            </ol>
        </section>
        <section id="chapter17">
            <h2 class="dark">Chapter 16: Multiple Companies</h2>
            <p class="mt-2 mid-dark">
                If you are an individual or firm that handles payroll preparation for more than one company, NETPIPO allows you to manage multiple companies from a single account. Follow these steps to manage multiple companies:
            </p>
            <ol>
                <li>Click on the "Switch Company" button on the dashboard</li>
                <li>Select the company you want to switch to from the drop-down list</li>
                <li>You will be redirected to the dashboard of the selected company</li>
            </ol>
        </section>
        <section id="chapter18">
            <h2 class="dark">Chapter 17: Support</h2>
            <p class="mt-2 mid-dark">
                If you have any questions or need assistance with NETPIPO, you can contact our support team for help.
            </p>
            <ul class="list-disc list-inside mt-2 mid-dark">
                <li>Live Chat: Available on the NETPIPO website</li>
                <li>Email: <EMAIL></li>
                <li>Phone: ********* | **********</li>
            </ul>
        </section>
    </div>
</div>
{% endblock %}
