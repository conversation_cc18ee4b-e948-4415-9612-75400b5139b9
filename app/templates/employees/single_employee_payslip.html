<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
<link rel="stylesheet" href="{{ url_for('static', filename='styles/employee_payslip.css') }}">
<div class="ls_container">
     <div class="dyn_header no-print">
        <div class="dynamic--buttons">
            <h1>{{ emp_data.employee.first_name }} {{ emp_data.employee.last_name }}'s Payslip</h1>
            <div class="print-area">
                <button onclick="printAndRefresh()">
                    <i class="fi fi-rr-print"></i>
                    <span>Print</span>
                </button>
    
                {% if emp_data.employee.email %}
                <a href="{{ url_for('my_employees.send_payslip_email', employee_id=emp_data.employee.employee_id) }}" class="email-btn">
                    <i class="fi fi-rr-envelope"></i>
                    <span>Email Payslip</span>
                </a>
                {% else %}
                <button class="email-btn disabled" title="Employee has no email address" disabled>
                    <i class="fi fi-rr-envelope"></i>
                    <span>Email Payslip</span>
                </button>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="dyn_container">
        <div class="payslip-content">
            <div class="payslip--header">
                <div class="company--logo-payslip">
                    {% if company_data.logo %}
                        {% if 'digitaloceanspaces.com' in company_data.logo %}
                            <!-- Use the full URL directly from DigitalOcean Spaces -->
                            <img src="{{ company_data.logo }}" alt="Company Logo">
                        {% else %}
                            <!-- Fallback for legacy logos stored on disk -->
                            <img src="{{ url_for('static', filename='uploads/logos/' + company_data.logo) }}" alt="Company Logo">
                        {% endif %}
                    {% else %}
                        <p></p>
                    {% endif %}
                </div>
                <div class="payslip">
                    <h1>PAYSLIP</h1>
                </div>
            </div>
            <div class="company--details">
                <table class="col--one">
                    <tr>
                        <td style="font-weight: bold;">
                           Name 
                        </td>
                        <td>
                            {{ company_data.company_name }}
                        </td>
                    </tr>
                    <tr>
                        <td style="font-weight: bold;">TIN: </td>
                        <td>
                            {{ company_data.company_tin }}
                        </td>
                    </tr>
                    <tr>
                        <td style="font-weight: bold;">RSSB No: </td>
                        <td>{{ company_data.rssb_number}}</td>
                    </tr>
                </table>
                <table class="col--two">
                    <tr>
                        <td style="font-weight: bold;">Phone: </td>
                        <td>  {% if company_data.phone_number and company_data.phone_number != 'None' %}
                            {{ company_data.phone_number }}
                        {% else %}
                        
                        {% endif %}</td>
                    </tr>
                    <tr>
                        <td style="font-weight: bold;">Email: </td>
                        <td>{{company_data.email}}</td>
                    </tr>
                    <tr>
                        <td style="font-weight: bold;">Address: </td>
                        <td>{{ company_data.province }}, {{company_data.district}}, {{company_data.sector}}</td>
                    </tr>
                </table>
            </div>
        <div class="combined--tables">
            <table class="payslip--table">
            <tr>
                <th class="table-title" colspan="4">EMPLOYEE PAYSLIP FOR THE MONTH OF {{ pay_date }}</th>
            </tr>
            <tr>
                <th>Employee Name</th>
                <td>{{ emp_data.employee.first_name }} {{ emp_data.employee.last_name }}</td>
                <th>Bank name</th>
                <td>
                    <!-- if bank name is none display nothing. -->
                    {% if emp_data.employee.bank_name %}
                        {{ emp_data.employee.bank_name }}
                    {% else %}

                    {% endif %}
                </td>
            </tr>
            <tr>
                <th>Position</th>
                <td>{% if emp_data.employee.job_title %}
                    {{ emp_data.employee.job_title }}
                {% else %}
                    <!-- If the job title is None display nothing. -->
                    {% endif %}
                </td>
                <th>Bank/Branch location</th>
                <td>
                    <!-- if branch name is none display nothing. -->
                    {% if emp_data.employee.bank_branch %}
                        {{ emp_data.employee.bank_branch }}
                    {% else %}

                    {% endif %}
                </td>
            </tr>
            <tr>
                <th>Employee ID</th>
                <td>{{ emp_data.employee.nid }}</td>
                <th>Bank Account No</th>
                <td>
                    <!-- if bank account is none display nothing. -->
                    {% if emp_data.employee.bank_account %}
                        {{ emp_data.employee.bank_account }}
                    {% else %}

                    {% endif %}
                </td>
            </tr>
            <tr>
                <th>Email</th>
                <td>
                    <!-- if email is none display nothing. -->
                    {% if emp_data.employee.email %}
                        {{ emp_data.employee.email }}
                    {% else %}

                    {% endif %}
                </td>
                <th>Bank Account Name</th>
                <td>
                    <!-- If the account name is None display nothing. -->
                    {% if emp_data.employee.bank_account_name %}
                        {{ emp_data.employee.bank_account_name }}
                    {% else %}

                    {% endif %}
                </td>
            </tr>
            <tr>
                <th>Department</th>
                <td>
                    <!-- if department is none display nothing. -->
                    {% if emp_data.employee.department %}
                        {{ emp_data.employee.department }}
                    {% elif emp_data.employee.department == 'NaN' %}
                        N/A
                    {% else %}

                    {% endif %}
                </td>
                <th>Currency</th>
                <td>RWF</td>
            </tr>
            <tr>
                <th>Telephone</th>
                <td>{% if emp_data.employee.phone %}
                    {{ emp_data.employee.phone }}
                {% else %}
                    <!-- If the phone number is None display nothing. -->
                    {% endif %}
                    
            </td>
                <th> Amount to Credit</th>
                <td>RWF {{ Auxillary.format_amount(emp_data.net_salary_value  + emp_data.total_reimbursements - emp_data.total_deductions) }} </td>
            </tr>
            <tr>
                <th>Joining Date</th>
                <td>
                    <!-- if hire date is none display nothing. -->
                    {% if emp_data.employee.hire_date %}
                        {{ emp_data.employee.hire_date }}
                    {% else %}

                    {% endif %}

                </td>
                <th>Days Worked</th>
                <td>{{ days_in_month }}</td>
            </tr>
            <tr>
                <th>RSSB Number</th>
                <td>{{ emp_data.employee.nsf }}</td>
                <th>Date from</th>
                <td>{{ first_day }}</td>
            </tr>
            <tr>
                <th>Location</th>
                <td>{{ company_data.province }}, {{company_data.district}}, {{company_data.sector}}</td>
                <th>Date to</th>
                <td>{{ last_day }}</td>

            </tr>
        </table>
        <div class="space">
            <table class="payslip--table">
                <tr>
                    <th colspan="2">
                        Monthly Earnings
                    </th>
                    <th colspan="2">
                        Monthly Deductions
                    </th>
                </tr>
                <tr>
                    <th>
                        Basic Salary
                    </th>
                    <td>
                        RWF {{ Auxillary.format_amount(emp_data.basic_needed) }}
                    </td>
                    <th>
                        Pay as you Earn (PAYE)
                    </th>
                    <td>
                        RWF {{ Auxillary.format_amount(emp_data.paye) }}
                    </td>
                </tr>
                <tr>
                    <th>Transport Allowance</th>
                    <td>RWF {{ Auxillary.format_amount(emp_data.employee.transport_allowance) }}</td>
                    <th>
                        Pension Contribution
                    </th>
                    <td>
                        RWF {{ Auxillary.format_amount(emp_data.pension_ee_value) }}
                    </td>
                </tr>
                <tr>
                    <th>
                        Living Allowance
                    </th>
                    <td>
                        RWF {{ Auxillary.format_amount(emp_data.employee.housing_allowance) }}
                    </td>
                    <th>
                        Maternity Contribution
                    </th>
                    <td>
                    RWF {{ Auxillary.format_amount(emp_data.maternity_ee_value) }}
                    </td>
                </tr>
                <tr>
                    <th>
                        Tel &amp; Communication Allowance
                    </th>
                    <td>
                        RWF {{ Auxillary.format_amount(emp_data.employee.communication_allowance) }}
                    </td>
                    <th>
                        Medical Contribution
                    </th>
                    <td>
                        RWF {{ Auxillary.format_amount(emp_data.rama_ee) }}
                    </td>
                </tr>
                <tr>
                    <th>
                        Overtime and Bonus
                    </th>
                    <td> RWF {{ Auxillary.format_amount(emp_data.employee.over_time) }}
                    </td>
                    <th>
                        RSSB CBHI
                    </th>
                    <td>
                        RWF {{ Auxillary.format_amount(emp_data.cbhi_value)}}
                    </td>
                </tr>
                <tr>
                    <th> Other Allowances
                    </th>
                    <td> RWF {{ Auxillary.format_amount(emp_data.employee.other_allowance) }}
                    </td>
                    <th>
                    Other Deductions
                    </th>
                    <td>
                        RWF {{ Auxillary.format_amount(emp_data.total_deductions) }}
                    </td>
                </tr>
                <tr>
                    <tr>
                        <th>

                        </th>
                        <td>
                        </td>
                        <th>
                            BRD Deduction
                        </th>
                        <td>
                            RWF {{ Auxillary.format_amount(emp_data.brd_deduction) }}
                        </td>
                    </tr>
                    <tr>
                        <th>

                        </th>
                        <td>
                        </td>
                        <th>
                            Salary Advance
                        </th>
                        <td>
                            RWF {{ Auxillary.format_amount(emp_data.salary_advance) }}
                        </td>
                    </tr>
                    <th>
                        GROSS SALARY (GS)
                    </th>
                    <td>
                        RWF {{ Auxillary.format_amount(emp_data.gross_needed) }}
                    </td>
                    <th>
                        Total Payroll Deductions (TPD)
                    </th>
                    <td>
                        RWF {{ Auxillary.format_amount(emp_data.total_deductions_value + emp_data.total_deductions + emp_data.cbhi_value + emp_data.brd_deduction + emp_data.salary_advance) }}
                    </td>
                </tr>
            </table>
            <table class="payslip--table">
                <tr>
                    <th>Net Salary (NS) = (GS-TPD)</th>
                    <td>RWF {{ Auxillary.format_amount(emp_data.net_salary_value - emp_data.total_deductions - emp_data.brd_deduction) }} </td>
                </tr>
                <tr>
                    <th>Total Reimbursements (Rbs)</th>
                    <td>RWF {{ Auxillary.format_amount(emp_data.total_reimbursements) }} </td>
                </tr>
                <tr>
                    <th>Net to Pay (NS+Rbs)</th>
                    <td>RWF {{ Auxillary.format_amount(emp_data.net_salary_value  + emp_data.total_reimbursements - emp_data.total_deductions - emp_data.brd_deduction - emp_data.salary_advance) }} </td></td>
                </tr>
                <tr>
                    <td colspan="2"><strong>Amount in Words:</strong> {{ Auxillary.number_to_words(emp_data.net_salary_value  + emp_data.total_reimbursements - emp_data.total_deductions - emp_data.brd_deduction - emp_data.salary_advance) }} Rwandan Francs</td>
                </tr>
            </table>
        </div>
    </div>
        <div class="payslip--footer">
            <div class="footer--column-one">
                <h3>Employee:</h3>
                <p>{{ emp_data.employee.first_name.upper() }} {{ emp_data.employee.last_name.upper() }}</td></p>
                <h3>Prepared by:</h3>
                <p>{{ first_name }} {{ last_name }}</p>

            </div>
            <div class="footer--column-two">
                <h3>Designation</h3>
                <p>{{ emp_data.employee.job_title}}</p>
                <p class="second_row">{{session.get('role').upper()}}</p>
            </div>
            <div class="footer--column-three">
                <h3>Signature and Date</h3>
                <p>____________________</p>
                <p class="second_row">____________________</p>
            </div>
        </div>
        <footer>
            <p id="disclaimer--content">
                <span id="disclaimer">Disclaimer:</span>This document is a system-generated payslip from {{ company_data.company_name }} and has been prepared in accordance with current Rwandan laws. If you find any errors, please contact our HR or finance department. Unauthorized disclosure, use, reproduction, sharing, or distribution of this document to third parties without prior written approval is prohibited . © {{ company_data.company_name }}, all rights reserved.
            </p>
        </footer>
    </div>
    <script src="{{ url_for('static', filename='scripts/payslip.js') }}"></script>

</div>
</div>
{% endblock %}
