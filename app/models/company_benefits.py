from sqlalchemy import Enum
from sqlalchemy import Boolean
from app.models.company_base import DynamicBase
import uuid
from sqlalchemy.orm import declarative_base
from sqlalchemy import Column, String, DateTime, ForeignKey, Float, Integer, Text, Numeric, UniqueConstraint, ForeignKeyConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy import Date
from datetime import datetime

class CompanyBenefit(DynamicBase):
    __tablename__ = "company_benefits"

    benefit_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    benefit_name = Column(String(128), nullable=False)
    benefit_amount = Column(Numeric(precision=18, scale=6), nullable=False)
    employer_contribution_fixed = Column(Numeric(precision=18, scale=6), nullable=True)
    employee_contribution_fixed = Column(Numeric(precision=18, scale=6), nullable=True)
    # add columns to track the percentages of employer or employee contributions will be a percentage
    employer_contribution_percentage = Column(Numeric(precision=18, scale=6), nullable=True)
    employee_contribution_percentage = Column(Numeric(precision=18, scale=6), nullable=True)
    benefit_start_date = Column(DateTime, nullable=False, default=datetime.now)
    benefit_end_date = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # Define the relationship between the company benefit and the benefit enrollments
    benefit_enrollments = relationship("BenefitEnrollment", back_populates="company_benefit", cascade="all, delete-orphan")
    

    def __repr__(self):
        """Return a string representation of the object."""
        return f"<CompanyBenefit(benefit_id={self.benefit_id}, benefit_name={self.benefit_name}, benefit_amount={self.benefit_amount}, benefit_start_date={self.benefit_start_date}, benefit_end_date={self.benefit_end_date})>"

    def to_dict(self):
        """Convert company benefit object to dictionary."""
        # get the contribution of the employee and employer depending on the input if
        # it is either a percentage or a fixed amount
        employer_contribution = (self.benefit_amount * self.employer_contribution_percentage / 100) if self.employer_contribution_percentage is not None else self.employer_contribution_fixed
        employee_contribution = (self.benefit_amount * self.employee_contribution_percentage / 100) if self.employee_contribution_percentage is not None else self.employee_contribution_fixed
        return {
            "benefit_id": self.benefit_id,
            "benefit_name": self.benefit_name,
            "benefit_amount": self.benefit_amount,
            "employer_contribution_fixed": self.employer_contribution_fixed,
            "employee_contribution_fixed": self.employee_contribution_fixed,
            "employer_contribution_percentage": self.employer_contribution_percentage,
            "employee_contribution_percentage": self.employee_contribution_percentage,
            "employee_contribution": employee_contribution,
            "employer_contribution": employer_contribution,
            "benefit_start_date": self.benefit_start_date.strftime('%d/%m/%Y'),
            "benefit_end_date": self.benefit_end_date.strftime('%d/%m/%Y') if self.benefit_end_date else None,
            "created_at": self.created_at.strftime('%d/%m/%Y'),
            "updated_at": self.updated_at.strftime('%d/%m/%Y'),
        }
    
    @classmethod
    def get_benefit_by_id(cls, db_session, benefit_id):
        """Get a benefit by ID."""
        return db_session.query(cls).get(benefit_id)
    @classmethod
    def get_benefits(cls, db_session):
        """Get all benefits from the database."""
        benefits = db_session.query(cls).all()
        return [benefit.to_dict() for benefit in benefits]
    
    @classmethod
    def add_benefit(cls, db_session, **kwargs):
        """Add a benefit to the database."""
        benefit = cls(**kwargs)
        db_session.add(benefit)
        db_session.commit()
        return benefit
    
    @classmethod
    def update_benefit(cls, db_session, benefit_id, **kwargs):
        """Update a benefit in the database."""
        benefit = db_session.query(cls).get(benefit_id)
        if not benefit:
            return None
        for k, v in kwargs.items():
            if hasattr(benefit, k):
                setattr(benefit, k, v)
        db_session.commit()
        return benefit
    
    @classmethod
    def delete_benefit(cls, db_session, benefit_id):
        """Delete a benefit from the database."""
        benefit = db_session.query(cls).get(benefit_id)
        if not benefit:
            return None
        db_session.delete(benefit)
        db_session.commit()
        return True
    
    @classmethod
    def get_benefit_by_name(cls, db_session, benefit_name):
        """Get a benefit by name."""
        benefit = db_session.query(cls).filter_by(benefit_name=benefit_name).first()
        return benefit
    
    @classmethod
    def get_benefit_by_id(cls, db_session, benefit_id):
        """Get a benefit by ID."""
        return db_session.query(cls).get(benefit_id)
    
class BenefitEnrollment(DynamicBase):
    __tablename__ = 'benefit_enrollments'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    employee_id = Column(UUID(as_uuid=True), ForeignKey('employees.employee_id', ondelete='CASCADE'), nullable=False)
    company_benefit_id = Column(UUID(as_uuid=True), ForeignKey('company_benefits.benefit_id', ondelete='CASCADE'), nullable=False)
    # Optional overrides
    custom_total_amount = Column(Numeric(10, 2), nullable=True)
    custom_employee_contribution = Column(Numeric(10, 2), nullable=True)
    custom_company_contribution = Column(Numeric(10, 2), nullable=True)
    start_date = Column(Date, nullable=False)
    end_date = Column(Date, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # Define the relationship between the benefit enrollment and the company benefit
    company_benefit = relationship("CompanyBenefit", back_populates="benefit_enrollments", foreign_keys=[company_benefit_id])

   # Make sure the employee does not get duplicate benefits
    __table_args__ = (UniqueConstraint('employee_id', 'company_benefit_id', name='unique_benefit_enrollment'), )



    def __repr__(self):
        """Return a string representation of the object."""
        return f"<BenefitEnrollment(id={self.id}, employee_id={self.employee_id}, company_benefit_id={self.company_benefit_id}, custom_total_amount={self.custom_total_amount}, custom_employee_contribution={self.custom_employee_contribution}, custom_company_contribution={self.custom_company_contribution}, start_date={self.start_date}, end_date={self.end_date})>"


    def to_dict(self):
        """Convert benefit enrollment object to dictionary."""
        return {
            "id": self.id,
            "employee_id": self.employee_id,
            "company_benefit_id": self.company_benefit_id,
            "custom_total_amount": self.custom_total_amount,
            "custom_employee_contribution": self.custom_employee_contribution,
            "custom_company_contribution": self.custom_company_contribution,
            "start_date": self.start_date.strftime('%d/%m/%Y'),
            "end_date": self.end_date.strftime('%d/%m/%Y') if self.end_date else None,
            "created_at": self.created_at.strftime('%d/%m/%Y'),
            "updated_at": self.updated_at.strftime('%d/%m/%Y'),
        }
    
    @classmethod
    def get_benefit_enrollments(cls, db_session):
        """Get all benefit enrollments from the database."""
        enrollments = db_session.query(cls).all()
        return [enrollment.to_dict() for enrollment in enrollments]


    @classmethod
    def get_benefit_enrollment_by_id(cls, db_session, enrollment_id):
        """Get a benefit enrollment by ID."""
        enrollment = db_session.query(cls).get(enrollment_id)
        return enrollment.to_dict()

    @classmethod
    def get_benefit_enrollments_by_employee_id(cls, db_session, employee_id):
        """Get all benefit enrollments for an employee."""
        enrollments = db_session.query(cls).filter_by(employee_id=employee_id).all()
        return [enrollment.to_dict() for enrollment in enrollments]

    @classmethod
    def get_benefit_enrollments_by_company_benefit_id(cls, db_session, company_benefit_id):
        """Get all benefit enrollments for a company benefit."""
        enrollments = db_session.query(cls).filter_by(company_benefit_id=company_benefit_id).all()
        return [enrollment.to_dict() for enrollment in enrollments]


    @classmethod
    def add_benefit_enrollment(cls, db_session, employee_id, company_benefit_id, custom_total_amount, custom_employee_contribution, custom_company_contribution, start_date, end_date):
        """Add a benefit enrollment to the database."""
        enrollment = cls(
            employee_id=employee_id,
            company_benefit_id=company_benefit_id,
            custom_total_amount=custom_total_amount,
            custom_employee_contribution=custom_employee_contribution,
            custom_company_contribution=custom_company_contribution,
            start_date=start_date,
            end_date=end_date
        )
        db_session.add(enrollment)
        db_session.commit()
        return enrollment
    
    @classmethod
    def update_benefit_enrollment(cls, db_session, enrollment_id, custom_total_amount, custom_employee_contribution, custom_company_contribution, start_date, end_date):
        """Update a benefit enrollment in the database."""
        enrollment = db_session.query(cls).get(enrollment_id)
        if not enrollment:
            return None
        enrollment.custom_total_amount = custom_total_amount
        enrollment.custom_employee_contribution = custom_employee_contribution
        enrollment.custom_company_contribution = custom_company_contribution
        enrollment.start_date = start_date
        enrollment.end_date = end_date
        db_session.commit()
        return enrollment


    @classmethod
    def delete_benefit_enrollment(cls, db_session, enrollment_id):
        """Delete a benefit enrollment from the database."""
        enrollment = db_session.query(cls).get(enrollment_id)
        if not enrollment:
            return None
        db_session.delete(enrollment)
        db_session.commit()
        return True

    @classmethod
    def get_benefit_enrollments_for_employee_for_current_month(cls, db_session, employee_id):
        """Get all benefit enrollments for an employee for the current month."""
        enrollments = db_session.query(cls).filter_by(employee_id=employee_id).all()
        return [enrollment.to_dict() for enrollment in enrollments]

    @classmethod
    def get_benefit_enrollments_for_employee_for_given_month_and_year(cls, db_session, employee_id, month, year):
        """Get all benefit enrollments for an employee for a given month and year."""
        enrollments = db_session.query(cls).filter_by(employee_id=employee_id).all()
        return [enrollment.to_dict() for enrollment in enrollments]

    @classmethod
    def get_benefit_enrollments_for_company_benefit_for_current_month(cls, db_session, company_benefit_id):
        """Get all benefit enrollments for a company benefit for the current month."""
        enrollments = db_session.query(cls).filter_by(company_benefit_id=company_benefit_id).all()
        return [enrollment.to_dict() for enrollment in enrollments]

    @classmethod
    def get_benefit_enrollments_for_company_benefit_for_given_month_and_year(cls, db_session, company_benefit_id, month, year):
        """Get all benefit enrollments for a company benefit for a given month and year."""
        enrollments = db_session.query(cls).filter_by(company_benefit_id=company_benefit_id).all()
        return [enrollment.to_dict() for enrollment in enrollments]

    @classmethod
    def get_benefit_enrollments_for_company_benefit_for_given_month_and_year_and_employee(cls, db_session, company_benefit_id, month, year, employee_id):
        """Get all benefit enrollments for a company benefit for a given month and year and employee."""
        enrollments = db_session.query(cls).filter_by(company_benefit_id=company_benefit_id, employee_id=employee_id).all()
        return [enrollment.to_dict() for enrollment in enrollments]


    @classmethod
    def get_benefit_enrollments_for_employee_for_given_month_and_year_and_company_benefit(cls, db_session, employee_id, month, year, company_benefit_id):
        """Get all benefit enrollments for an employee for a given month and year and company benefit."""
        enrollments = db_session.query(cls).filter_by(employee_id=employee_id, company_benefit_id=company_benefit_id).all()
        return [enrollment.to_dict() for enrollment in enrollments]

    @classmethod
    def get_benefit_enrollments_for_employee_for_given_month_and_year_and_company_benefit_and_custom_total_amount(cls, db_session, employee_id, month, year, company_benefit_id, custom_total_amount):
        """Get all benefit enrollments for an employee for a given month and year and company benefit and custom total amount."""
        enrollments = db_session.query(cls).filter_by(employee_id=employee_id, company_benefit_id=company_benefit_id, custom_total_amount=custom_total_amount).all()
        return [enrollment.to_dict() for enrollment in enrollments]

    @classmethod
    def get_benefit_enrollments_for_employee_for_given_month_and_year_and_company_benefit_and_custom_employee_contribution(cls, db_session, employee_id, month, year, company_benefit_id, custom_employee_contribution):
        """Get all benefit enrollments for an employee for a given month and year and company benefit and custom employee contribution."""
        enrollments = db_session.query(cls).filter_by(employee_id=employee_id, company_benefit_id=company_benefit_id, custom_employee_contribution=custom_employee_contribution).all()
        return [enrollment.to_dict() for enrollment in enrollments]

    @classmethod
    def get_benefit_enrollments_for_employee_for_given_month_and_year_and_company_benefit_and_custom_company_contribution(cls, db_session, employee_id, month, year, company_benefit_id, custom_company_contribution):
        """Get all benefit enrollments for an employee for a given month and year and company benefit and custom company contribution."""
        enrollments = db_session.query(cls).filter_by(employee_id=employee_id, company_benefit_id=company_benefit_id, custom_company_contribution=custom_company_contribution).all()
        return [enrollment.to_dict() for enrollment in enrollments]