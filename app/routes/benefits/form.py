from flask_wtf import FlaskForm
from wtforms.validators import DataRequired, Optional
from wtforms import DecimalField, SubmitField, StringField, TextAreaField, DateField, SelectField, SelectMultipleField
from wtforms.widgets import CheckboxInput, ListWidget

class BenefitForm(FlaskForm):
    benefit_name = StringField('Benefit Name', validators=[DataRequired()])
    benefit_amount = DecimalField('Benefit Amount', validators=[DataRequired()])
    employer_contribution_fixed = DecimalField('Employer Contribution Fixed', validators=[Optional()])
    employee_contribution_fixed = DecimalField('Employee Contribution Fixed', validators=[Optional()])
    employer_contribution_percentage = DecimalField('Employer Contribution Percentage', validators=[Optional()])
    employee_contribution_percentage = DecimalField('Employee Contribution Percentage', validators=[Optional()])
    benefit_start_date = DateField('Benefit Start Date', validators=[DataRequired()])
    benefit_end_date = DateField('Benefit End Date', validators=[Optional()])
    submit = SubmitField('Save and Close')

class BenefitEnrollmentForm(FlaskForm):
    employee_id = StringField('Employee ID', validators=[DataRequired()])
    company_benefit_id = StringField('Company Benefit ID', validators=[DataRequired()])
    custom_total_amount = DecimalField('Custom Total Amount', validators=[Optional()])
    custom_employee_contribution = DecimalField('Custom Employee Contribution', validators=[Optional()])
    custom_company_contribution = DecimalField('Custom Company Contribution', validators=[Optional()])
    start_date = DateField('Start Date', validators=[DataRequired()])
    end_date = DateField('End Date', validators=[Optional()])
    submit = SubmitField('Save and Close')
    