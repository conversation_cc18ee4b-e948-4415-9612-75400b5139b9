from flask import Blueprint, request, jsonify, current_app, flash, redirect, url_for, render_template
from app.models.company_benefits import CompanyBenefit, BenefitEnrollment
from app.decorators.role_decorator import role_required
from app.utils.db_connection import DatabaseConnection
from flask import session, current_app as app
from app.routes.benefits.form import BenefitForm
from app.helpers.auxillary import Auxillary

benefits_bp = Blueprint('benefits', __name__)

@benefits_bp.route('/benefits', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def benefits():
    """View all benefits."""
    # Get database name
    database_name = session.get('database_name')
    # Create a new database connection
    db_connection = DatabaseConnection()

    with db_connection.get_session(database_name) as db_session:
        try:
            app.logger.info("Getting all benefits")
            benefits = CompanyBenefit.get_benefits(db_session)
            app.logger.info(f"Benefits: {benefits}")
            return render_template('benefits/benefits.html', benefits=benefits)
        except Exception as e:
            message = f"An error occurred while getting all benefits: {str(e)}"
            flash(message, 'danger')
            app.logger.error(message)
            return redirect(url_for('admin_data.dashboard'))

@benefits_bp.route('/add_benefit', methods=['GET', 'POST'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def add_benefit():
    """Add a new benefit."""
    form = BenefitForm()
    if request.method == 'POST':
        if not form.validate_on_submit():
            flash('Please check your input', 'danger')
            return redirect(url_for('benefits.add_benefit'))
        data = request.form
        benefit_name = data.get('benefit_name')
        benefit_amount = data.get('benefit_amount')
        employer_contribution_fixed = data.get('employer_contribution_fixed')
        employee_contribution_fixed = data.get('employee_contribution_fixed')
        employer_contribution_percentage = data.get('employer_contribution_percentage')
        employee_contribution_percentage = data.get('employee_contribution_percentage')
        benefit_start_date = data.get('benefit_start_date')
        benefit_end_date = data.get('benefit_end_date')

        #convert the amount to decimal is the value is not empty
        if benefit_amount is not None or benefit_amount != '':
            benefit_amount = Auxillary.to_decimal(benefit_amount)
        if employer_contribution_fixed is not None or employer_contribution_fixed != '':
            employer_contribution_fixed = Auxillary.to_decimal(employer_contribution_fixed)
        if employee_contribution_fixed is not None or employee_contribution_fixed != '':
            employee_contribution_fixed = Auxillary.to_decimal(employee_contribution_fixed)
        if employer_contribution_percentage is not None or employer_contribution_percentage != '':
            employer_contribution_percentage = Auxillary.to_decimal(employer_contribution_percentage)
        if employee_contribution_percentage is not None or employee_contribution_percentage != '':
            employee_contribution_percentage = Auxillary.to_decimal(employee_contribution_percentage)

        # Get database name
        database_name = session.get('database_name')
        # Create a new database connection
        db_connection = DatabaseConnection()

        with db_connection.get_session(database_name) as db_session:
            # Construct the benefit
            benefit_data = {
                "benefit_name": benefit_name,
                "benefit_amount": benefit_amount,
                "employer_contribution_fixed": employer_contribution_fixed or None,
                "employee_contribution_fixed": employee_contribution_fixed or None,
                "employer_contribution_percentage": employer_contribution_percentage or None,
                "employee_contribution_percentage": employee_contribution_percentage or None,
                "benefit_start_date": benefit_start_date,
                "benefit_end_date": benefit_end_date or None
            }
            try:
                app.logger.info("Adding benefit")
                benefit = CompanyBenefit.add_benefit(db_session, **benefit_data)
                app.logger.info(f"Benefit: {benefit}")
                flash('Benefit added successfully', 'success')
                return redirect(url_for('benefits.benefits'))
            except Exception as e:
                message = f"An error occurred while adding benefit: {str(e)}"
                flash(message, 'danger')
                app.logger.error(message)
                return redirect(url_for('benefits.add_benefit'))
    try:
        return render_template('benefits/add_benefit.html', form=form)
    except Exception as e:
        message = f"An error occurred while rendering the template: {str(e)}"
        flash(message, 'danger')
        app.logger.error(message)
        return redirect(url_for('admin_data.dashboard'))

