from flask import Blueprint, request, jsonify, current_app, flash, redirect, url_for, render_template
from app.models.company_benefits import CompanyBenefit, BenefitEnrollment
from app.decorators.role_decorator import role_required
from app.utils.db_connection import DatabaseConnection
from flask import session, current_app as app
from app.routes.benefits.form import BenefitForm, BenefitEnrollmentForm
from app.helpers.auxillary import Auxillary

benefits_bp = Blueprint('benefits', __name__)

@benefits_bp.route('/benefits', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def benefits():
    """View all benefits."""
    # Get database name
    database_name = session.get('database_name')
    # Create a new database connection
    db_connection = DatabaseConnection()

    with db_connection.get_session(database_name) as db_session:
        try:
            app.logger.info("Getting all benefits")
            benefits = CompanyBenefit.get_benefits(db_session)
            app.logger.info(f"Benefits: {benefits}")
            return render_template('benefits/benefits.html', benefits=benefits)
        except Exception as e:
            message = f"An error occurred while getting all benefits: {str(e)}"
            flash(message, 'danger')
            app.logger.error(message)
            return redirect(url_for('admin_data.dashboard'))

@benefits_bp.route('/add_benefit', methods=['GET', 'POST'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def add_benefit():
    """Add a new benefit."""
    form = BenefitForm()
    if request.method == 'POST':
        if not form.validate_on_submit():
            flash('Please check your input', 'danger')
            return redirect(url_for('benefits.add_benefit'))
        data = request.form
        benefit_name = data.get('benefit_name')
        benefit_amount = data.get('benefit_amount')
        employer_contribution_fixed = data.get('employer_contribution_fixed') or None
        employee_contribution_fixed = data.get('employee_contribution_fixed') or None
        employer_contribution_percentage = data.get('employer_contribution_percentage') or None
        employee_contribution_percentage = data.get('employee_contribution_percentage') or None
        benefit_start_date = data.get('benefit_start_date')
        benefit_end_date = data.get('benefit_end_date') or None

        # Convert the amount to decimal if the value is not empty
        benefit_amount = Auxillary.to_decimal(benefit_amount) if benefit_amount else None
        employer_contribution_fixed = Auxillary.to_decimal(employer_contribution_fixed) if employer_contribution_fixed else None
        employee_contribution_fixed = Auxillary.to_decimal(employee_contribution_fixed) if employee_contribution_fixed else None
        employer_contribution_percentage = Auxillary.to_decimal(employer_contribution_percentage) if employer_contribution_percentage else None
        employee_contribution_percentage = Auxillary.to_decimal(employee_contribution_percentage) if employee_contribution_percentage else None

        # Get database name
        database_name = session.get('database_name')
        # Create a new database connection
        db_connection = DatabaseConnection()

        with db_connection.get_session(database_name) as db_session:
            # Construct the benefit
            benefit_data = {
                "benefit_name": benefit_name,
                "benefit_amount": benefit_amount,
                "employer_contribution_fixed": employer_contribution_fixed,
                "employee_contribution_fixed": employee_contribution_fixed,
                "employer_contribution_percentage": employer_contribution_percentage,
                "employee_contribution_percentage": employee_contribution_percentage,
                "benefit_start_date": benefit_start_date,
                "benefit_end_date": benefit_end_date
            }
            try:
                app.logger.info("Adding benefit")
                benefit = CompanyBenefit.add_benefit(db_session, **benefit_data)
                app.logger.info(f"Benefit: {benefit}")
                flash('Benefit added successfully', 'success')
                return redirect(url_for('benefits.benefits'))
            except Exception as e:
                message = f"An error occurred while adding benefit: {str(e)}"
                flash(message, 'danger')
                app.logger.error(message)
                return redirect(url_for('benefits.add_benefit'))
    try:
        return render_template('benefits/add_benefit.html', form=form)
    except Exception as e:
        message = f"An error occurred while rendering the template: {str(e)}"
        flash(message, 'danger')
        app.logger.error(message)
        return redirect(url_for('admin_data.dashboard'))

@benefits_bp.route('/update_benefit/<benefit_id>', methods=['GET', 'POST'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def update_benefit(benefit_id):
    """Update an existing benefit."""
    # Get database name
    database_name = session.get('database_name')
    # Create a new database connection
    db_connection = DatabaseConnection()

    with db_connection.get_session(database_name) as db_session:
        try:
            # Get the existing benefit
            benefit = CompanyBenefit.get_benefit_by_id(db_session, benefit_id)
            if not benefit:
                flash('Benefit not found', 'danger')
                return redirect(url_for('benefits.benefits'))

            form = BenefitForm()

            if request.method == 'POST':
                if not form.validate_on_submit():
                    flash('Please check your input', 'danger')
                    return redirect(url_for('benefits.update_benefit', benefit_id=benefit_id))

                data = request.form
                benefit_name = data.get('benefit_name')
                benefit_amount = data.get('benefit_amount')
                employer_contribution_fixed = data.get('employer_contribution_fixed') or None
                employee_contribution_fixed = data.get('employee_contribution_fixed') or None
                employer_contribution_percentage = data.get('employer_contribution_percentage') or None
                employee_contribution_percentage = data.get('employee_contribution_percentage') or None
                benefit_start_date = data.get('benefit_start_date')
                benefit_end_date = data.get('benefit_end_date') or None

                # Convert the amount to decimal if the value is not empty
                benefit_amount = Auxillary.to_decimal(benefit_amount) if benefit_amount else None
                employer_contribution_fixed = Auxillary.to_decimal(employer_contribution_fixed) if employer_contribution_fixed else None
                employee_contribution_fixed = Auxillary.to_decimal(employee_contribution_fixed) if employee_contribution_fixed else None
                employer_contribution_percentage = Auxillary.to_decimal(employer_contribution_percentage) if employer_contribution_percentage else None
                employee_contribution_percentage = Auxillary.to_decimal(employee_contribution_percentage) if employee_contribution_percentage else None

                # Update the benefit
                update_data = {
                    "benefit_name": benefit_name,
                    "benefit_amount": benefit_amount,
                    "employer_contribution_fixed": employer_contribution_fixed,
                    "employee_contribution_fixed": employee_contribution_fixed,
                    "employer_contribution_percentage": employer_contribution_percentage,
                    "employee_contribution_percentage": employee_contribution_percentage,
                    "benefit_start_date": benefit_start_date,
                    "benefit_end_date": benefit_end_date
                }

                try:
                    app.logger.info("Updating benefit")
                    updated_benefit = CompanyBenefit.update_benefit(db_session, benefit_id, **update_data)
                    if updated_benefit:
                        app.logger.info(f"Benefit updated: {updated_benefit}")
                        flash('Benefit updated successfully', 'success')
                        return redirect(url_for('benefits.benefits'))
                    else:
                        flash('Failed to update benefit', 'danger')
                        return redirect(url_for('benefits.update_benefit', benefit_id=benefit_id))
                except Exception as e:
                    message = f"An error occurred while updating benefit: {str(e)}"
                    flash(message, 'danger')
                    app.logger.error(message)
                    return redirect(url_for('benefits.update_benefit', benefit_id=benefit_id))

            # Pre-populate form with existing data for GET request
            form.benefit_name.data = benefit.benefit_name
            form.benefit_amount.data = benefit.benefit_amount
            form.employer_contribution_fixed.data = benefit.employer_contribution_fixed
            form.employee_contribution_fixed.data = benefit.employee_contribution_fixed
            form.employer_contribution_percentage.data = benefit.employer_contribution_percentage
            form.employee_contribution_percentage.data = benefit.employee_contribution_percentage
            form.benefit_start_date.data = benefit.benefit_start_date.date() if benefit.benefit_start_date else None
            form.benefit_end_date.data = benefit.benefit_end_date.date() if benefit.benefit_end_date else None

            return render_template('benefits/edit_benefit.html', form=form, benefit=benefit)
        except Exception as e:
            message = f"An error occurred while loading benefit: {str(e)}"
            flash(message, 'danger')
            app.logger.error(message)
            return redirect(url_for('benefits.benefits'))

@benefits_bp.route('/delete_benefit/<benefit_id>', methods=['GET', 'POST'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def delete_benefit(benefit_id):
    """Delete a benefit."""
    # Get database name
    database_name = session.get('database_name')
    # Create a new database connection
    db_connection = DatabaseConnection()

    with db_connection.get_session(database_name) as db_session:
        try:
            # Get the existing benefit
            benefit = CompanyBenefit.get_benefit_by_id(db_session, benefit_id)
            if not benefit:
                flash('Benefit not found', 'danger')
                return redirect(url_for('benefits.benefits'))

            if request.method == 'POST':
                try:
                    app.logger.info(f"Deleting benefit: {benefit_id}")
                    result = CompanyBenefit.delete_benefit(db_session, benefit_id)
                    if result:
                        app.logger.info(f"Benefit deleted successfully: {benefit_id}")
                        flash('Benefit deleted successfully', 'success')
                        return redirect(url_for('benefits.benefits'))
                    else:
                        flash('Failed to delete benefit', 'danger')
                        return redirect(url_for('benefits.delete_benefit', benefit_id=benefit_id))
                except Exception as e:
                    message = f"An error occurred while deleting benefit: {str(e)}"
                    flash(message, 'danger')
                    app.logger.error(message)
                    return redirect(url_for('benefits.delete_benefit', benefit_id=benefit_id))

            # For GET request, show confirmation page
            benefit_dict = benefit.to_dict()
            return render_template('benefits/delete_benefit.html', benefit=benefit_dict)
        except Exception as e:
            message = f"An error occurred while loading benefit: {str(e)}"
            flash(message, 'danger')
            app.logger.error(message)
            return redirect(url_for('benefits.benefits'))

@benefits_bp.route('/view_benefit/<benefit_id>', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def view_benefit(benefit_id):
    """View detailed information about a single benefit."""
    # Get database name
    database_name = session.get('database_name')
    # Create a new database connection
    db_connection = DatabaseConnection()

    with db_connection.get_session(database_name) as db_session:
        try:
            # Get the existing benefit
            benefit = CompanyBenefit.get_benefit_by_id(db_session, benefit_id)
            if not benefit:
                flash('Benefit not found', 'danger')
                return redirect(url_for('benefits.benefits'))

            # Get enrollment statistics
            enrollments = BenefitEnrollment.get_benefit_enrollments_for_company_benefit_for_current_month(db_session, benefit_id)

            benefit_dict = benefit.to_dict()
            enrollment_count = len(enrollments)

            return render_template('benefits/view_benefit.html',
                                 benefit=benefit_dict,
                                 enrollments=enrollments,
                                 enrollment_count=enrollment_count)
        except Exception as e:
            message = f"An error occurred while loading benefit details: {str(e)}"
            flash(message, 'danger')
            app.logger.error(message)
            return redirect(url_for('benefits.benefits'))

@benefits_bp.route('/enroll_benefit', methods=['GET', 'POST'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def enroll_benefit():
    """Enroll an employee in a benefit."""
    form = BenefitEnrollmentForm()
    # Get database name
    database_name = session.get('database_name')
    # Create a new database connection
    db_connection = DatabaseConnection()

    with db_connection.get_session(database_name) as db_session:
        try:
            if request.method == 'POST':
                if not form.validate_on_submit():
                    # Show the errors in the form
                    errors = []
                    for field, errors in form.errors.items():
                        for error in errors:
                            errors.append(f"{field}: {error}")
                    for error in errors:
                        flash(error, 'danger')
                    return redirect(url_for('benefits.enroll_benefit'))
                data = request.form
                employee_id = data.get('employee_id')
                company_benefit_id = data.get('company_benefit_id')
                custom_total_amount = data.get('custom_total_amount') or None
                custom_employee_contribution = data.get('custom_employee_contribution') or None
                custom_company_contribution = data.get('custom_company_contribution') or None
                start_date = data.get('start_date')
                end_date = data.get('end_date') or None

                # Convert the amounts to decimal if the value is not empty
                custom_total_amount = Auxillary.to_decimal(custom_total_amount) if custom_total_amount else None
                custom_employee_contribution = Auxillary.to_decimal(custom_employee_contribution) if custom_employee_contribution else None
                custom_company_contribution = Auxillary.to_decimal(custom_company_contribution) if custom_company_contribution else None

                # Add the benefit enrollment
                try:
                    app.logger.info("Adding benefit enrollment")
                    enrollment = BenefitEnrollment.add_benefit_enrollment(db_session, employee_id, company_benefit_id, custom_total_amount, custom_employee_contribution, custom_company_contribution, start_date, end_date)
                    if enrollment:
                        app.logger.info(f"Benefit enrollment added: {enrollment}")
                        flash('Benefit enrollment added successfully', 'success')
                        return redirect(url_for('benefits.benefits'))
                    else:
                        flash('Failed to add benefit enrollment', 'danger')
                        return redirect(url_for('benefits.enroll_benefit'))
                except Exception as e:
                    message = f"An error occurred while adding benefit enrollment: {str(e)}"
                    flash(message, 'danger')
                    app.logger.error(message)
                    return redirect(url_for('benefits.enroll_benefit'))
        except Exception as e:
            message = f"An error occurred while enrolling benefit: {str(e)}"
            flash(message, 'danger')
            app.logger.error(message)
            return redirect(url_for('benefits.benefits'))
        return render_template('benefits/enroll_benefit.html', form=form)
    
@benefits_bp.route('/view_benefit_enrollment/<enrollment_id>', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def view_benefit_enrollment(enrollment_id):
    """View detailed information about a single benefit enrollment."""
    # Get database name
    database_name = session.get('database_name')
    # Create a new database connection
    db_connection = DatabaseConnection()

    with db_connection.get_session(database_name) as db_session:
        try:
            # Get the existing benefit enrollment
            enrollment = BenefitEnrollment.get_benefit_enrollment_by_id(db_session, enrollment_id)
            if not enrollment:
                flash('Benefit enrollment not found', 'danger')
                return redirect(url_for('benefits.benefits'))

            enrollment_dict = enrollment.to_dict()
            return render_template('benefits/view_benefit_enrollment.html', enrollment=enrollment_dict)
        except Exception as e:
            message = f"An error occurred while loading benefit enrollment details: {str(e)}"
            flash(message, 'danger')
            app.logger.error(message)
            return redirect(url_for('benefits.benefits'))
    
@benefits_bp.route('/delete_benefit_enrollment/<enrollment_id>', methods=['GET', 'POST'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def delete_benefit_enrollment(enrollment_id):
    """Delete a benefit enrollment."""
    # Get database name
    database_name = session.get('database_name')
    # Create a new database connection
    db_connection = DatabaseConnection()

    with db_connection.get_session(database_name) as db_session:
        try:
            # Get the existing benefit enrollment
            enrollment = BenefitEnrollment.get_benefit_enrollment_by_id(db_session, enrollment_id)
            if not enrollment:
                flash('Benefit enrollment not found', 'danger')
                return redirect(url_for('benefits.benefits'))

            if request.method == 'POST':
                try:
                    app.logger.info(f"Deleting benefit enrollment: {enrollment_id}")
                    result = BenefitEnrollment.delete_benefit_enrollment(db_session, enrollment_id)
                    if result:
                        app.logger.info(f"Benefit enrollment deleted successfully: {enrollment_id}")
                        flash('Benefit enrollment deleted successfully', 'success')
                        return redirect(url_for('benefits.benefits'))
                    else:
                        flash('Failed to delete benefit enrollment', 'danger')
                        return redirect(url_for('benefits.delete_benefit_enrollment', enrollment_id=enrollment_id))
                except Exception as e:
                    message = f"An error occurred while deleting benefit enrollment: {str(e)}"
                    flash(message, 'danger')
                    app.logger.error(message)
                    return redirect(url_for('benefits.delete_benefit_enrollment', enrollment_id=enrollment_id))

            # For GET request, show confirmation page
            enrollment_dict = enrollment.to_dict()
            return render_template('benefits/delete_benefit_enrollment.html', enrollment=enrollment_dict)
        except Exception as e:
            message = f"An error occurred while loading benefit enrollment: {str(e)}"
            flash(message, 'danger')
            app.logger.error(message)
            return redirect(url_for('benefits.benefits'))
        
@benefits_bp.route('/update_benefit_enrollment/<enrollment_id>', methods=['GET', 'POST'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def update_benefit_enrollment(enrollment_id):
    """Update a benefit enrollment."""
    form = BenefitEnrollmentForm()
    # Get database name
    database_name = session.get('database_name')
    # Create a new database connection
    db_connection = DatabaseConnection()

    with db_connection.get_session(database_name) as db_session:
        try:
            # Get the existing benefit enrollment
            enrollment = BenefitEnrollment.get_benefit_enrollment_by_id(db_session, enrollment_id)
            if not enrollment:
                flash('Benefit enrollment not found', 'danger')
                return redirect(url_for('benefits.benefits'))

            if request.method == 'POST':
                if not form.validate_on_submit():
                    # Show the errors in the form
                    errors = []
                    for field, errors in form.errors.items():
                        for error in errors:
                            errors.append(f"{field}: {error}")
                    for error in errors:
                        flash(error, 'danger')
                    return redirect(url_for('benefits.update_benefit_enrollment', enrollment_id=enrollment_id))
                data = request.form
                custom_total_amount = data.get('custom_total_amount') or None
                custom_employee_contribution = data.get('custom_employee_contribution') or None
                custom_company_contribution = data.get('custom_company_contribution') or None
                start_date = data.get('start_date')
                end_date = data.get('end_date') or None

                # Convert the amounts to decimal if the value is not empty
                custom_total_amount = Auxillary.to_decimal(custom_total_amount) if custom_total_amount else None
                custom_employee_contribution = Auxillary.to_decimal(custom_employee_contribution) if custom_employee_contribution else None
                custom_company_contribution = Auxillary.to_decimal(custom_company_contribution) if custom_company_contribution else None

                # Update the benefit enrollment
                try:
                    app.logger.info("Updating benefit enrollment")
                    enrollment = BenefitEnrollment.update_benefit_enrollment(db_session, enrollment_id, custom_total_amount, custom_employee_contribution, custom_company_contribution, start_date, end_date)
                    if enrollment:
                        app.logger.info(f"Benefit enrollment updated: {enrollment}")
                        flash('Benefit enrollment updated successfully', 'success')
                        return redirect(url_for('benefits.benefits'))
                    else:
                        flash('Failed to update benefit enrollment', 'danger')
                        return redirect(url_for('benefits.update_benefit_enrollment', enrollment_id=enrollment_id))
                except Exception as e:
                    message = f"An error occurred while updating benefit enrollment: {str(e)}"
                    flash(message, 'danger')
                    app.logger.error(message)
                    return redirect(url_for('benefits.update_benefit_enrollment', enrollment_id=enrollment_id))
        except Exception as e:
            message = f"An error occurred while loading benefit enrollment: {str(e)}"
            flash(message, 'danger')
            app.logger.error(message)
            return redirect(url_for('benefits.benefits'))
        return render_template('benefits/update_benefit_enrollment.html', form=form)
    
    

