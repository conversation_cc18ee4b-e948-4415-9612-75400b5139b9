from flask import current_app, url_for, render_template, request, redirect, flash, jsonify, Blueprint, session
from app.models.company import DynamicBase, Employee, Departments, Site, Attendance
from app.utils.db_connection import DatabaseConnection
from app.helpers.company_helpers import CompanyHelpers
from app.routes.employees.forms import EmployeeForm, EmployeeUpdateForm, UploadEmployeeForm, EmployeePercentageForm
from app.decorators.hr_decorator import hr_required
from app.models.central import EmployeeType
from flask import send_file
from io import BytesIO
import pandas as pd
from openpyxl import load_workbook
from app.models.central import Company, Plans
from app.helpers.auxillary import Auxillary
from datetime import datetime, timedelta, date
import calendar
import logging
from app.decorators.role_decorator import role_required
from uuid import UUID
import os
from app.helpers.route_helpers import restrict_based_on_plan
from app.routes.payroll.goal_Seek_mine import SalaryCalculatorGross
import traceback
import requests
from app.helpers.pdf_generator import PDFGenerator
from app.helpers.auxillary import Auxillary
import tempfile
from app.models.company import Payroll
from app.services.percentage_salary_calculator import PercentageBasedSalaryCalculator
from decimal import Decimal, InvalidOperation


netpipo_base_url = os.getenv('NETPIPO_BASE_URL')


my_employees_bp = Blueprint('my_employees', __name__)

my_employees_bp.before_request(restrict_based_on_plan)

@my_employees_bp.route('/v2/register_employees', methods=['POST', 'GET'])
@role_required(['hr', 'accountant', 'manager', 'company_hr'])
def register_employees():
    """Register employee in the company."""
    form = EmployeeForm()
    # Fetch employee types from the database
    try:
        employee_types = EmployeeType.get_employee_types()
        form.employee_type.choices = [ type['employee_type_name'] for type in employee_types]
    except Exception as e:
        current_app.logger.error(f"Error fetching employee types: {e}")
        employee_types = []
        form.employee_type.choices = []

    # Get the companies plan from session
    plan_id = session.get('company_plan_id')
    current_app.logger.info(f"Plan ID: {plan_id}")

    # Check the plan details
    try:
        plan = Plans.get_plan_by_id(plan_id)
        current_app.logger.info(f"Plan: {plan}")
        if not plan:
            current_app.logger.error("No plan found")

        number_of_employees = plan['num_of_employees']
        current_app.logger.info(f"Number of employees: {number_of_employees}")
        current_app.logger.info(f"Type of number of employees: {type(number_of_employees)}")
    except Exception as e:
        current_app.logger.error(f"Error fetching plan: {e}")
        number_of_employees = 0

    # Initialize the database connection and retrive the departments
    db_connection = DatabaseConnection()
    database_name = session.get('database_name')
    try:
        with db_connection.get_session(database_name) as db_session:
            departments = Departments.get_departments(db_session)
            if not departments:
                current_app.logger.info("No departments found")
            form.department.choices = [('', 'Select Department')] + [(department['department_name'], department['department_name']) for department in departments]
    except Exception as e:
        current_app.logger.error(f"Error fetching departments: {e}")
        departments = []
        form.department.choices = []

    if request.method == 'POST':
        current_app.logger.info("POST request in register_employees")
        # validate the form
        if not form.validate_on_submit():
            form = EmployeeForm(request.form)
            return render_template('employees/register_employees_v2.html', form=form)
        current_app.logger.info("Form validated successfully")

        data = request.form
        current_app.logger.info(f"Form data: {data}")

        # Extract form data and convert empty strings to None
        first_name = data.get('first_name') or None
        last_name = data.get('last_name') or None
        nid = data.get('nid') or None
        rssb_number = data.get('rssb_number') or None
        bank_name = data.get('bank_name') or None
        bank_account = data.get('bank_account') or None
        branch_name = data.get('branch_name') or None
        account_name = data.get('account_name') or None
        birth_date = data.get('birth_date') or None
        marital_status = data.get('marital_status') or None
        gender = data.get('gender') or None
        employee_tin = data.get('employee_tin') or None
        employee_type = data.get('employee_type') or None
        department = data.get('department') or None
        salary_type = data.get('salary_type') or None
        salary_amount = data.get('salary_amount') or None

        transport_allowance = data.get('transport_allowance') or None
        housing_allowance = data.get('housing_allowance') or None
        communication_allowance = data.get('communication_allowance') or None
        over_time = data.get('over_time') or None
        other_allowance = data.get('other_allowance') or None
        email = data.get('email') or None
        phone = data.get('phone') or None
        job_title = data.get('job_title') or None
        hire_date = data.get('hire_date') or None
        contract_end_date = data.get('contract_end_date') or None

        # Handle annual leave balance (Numeric) and extra leave days (Integer) with proper error handling
        try:
            annual_leave_balance_str = data.get("annual_leave_balance", "0").strip()
            annual_leave_balance = Decimal(annual_leave_balance_str) if annual_leave_balance_str else Decimal('0')
        except (ValueError, TypeError, InvalidOperation):
            annual_leave_balance = Decimal('0')

        try:
            extra_leave_days_str = data.get("extra_leave_days", "0").strip()
            extra_leave_days = int(extra_leave_days_str) if extra_leave_days_str else 0
        except (ValueError, TypeError):
            extra_leave_days = 0

        current_app.logger.info(f"email: {email}, Annual Leave Balance before: {annual_leave_balance}, \
                                extra leave days: {extra_leave_days}")

        # Note: Empty strings are already converted to None during data extraction above
        current_app.logger.info(f"email: {email}")
        current_app.logger.info(f"phone: {phone}")
        if salary_type == 'net_salary':
            # set the salary amount depending on the salary type
            net_salary = salary_amount
            gross_salary = None
            total_staff_cost = None
        elif salary_type == 'gross_salary':
            gross_salary = salary_amount
            net_salary = None
            total_staff_cost = None
        elif salary_type == 'total_staff_cost':
            total_staff_cost = salary_amount
            gross_salary = None
            net_salary = None

        try:
            # Get the company_id from the session
            company_id = session.get('company_id')
            if not company_id:
                print("Company ID is missing in session")
                current_app.logger.error("Company ID is missing in session")
                return redirect(url_for('admin_data.dashboard'))
            # Get the company database name
            database_name = session.get('database_name')
            if not database_name:
                current_app.logger.error("Database name could not be retrieved")
                return redirect(url_for('admin_data.dashboard'))

            # Initialize the database connection and save the employee record
            db_connection = DatabaseConnection()
            try:
                with db_connection.get_session(database_name) as db_session:
                    # First check for any unique constraints before adding the employee
                    unique = Employee.check_unique(db_session, nid, rssb_number, email, phone)
                    current_app.logger.info(f"Unique: {unique}")
                    if unique:
                        current_app.logger.error(f"Error while saving employee: {unique}")
                        message = f"Error while saving employee: {unique}"
                        flash(unique, 'danger')
                        return render_template('employees/register_employees_v2.html', form=form)
                    # Retrieve the employees so that we can count them
                    employees = Employee.get_employees(db_session)
                    current_app.logger.info(f"length of employees: {len(employees)}")
                    current_app.logger.info(f"number of employees: {number_of_employees}")
                    if len(employees) >= number_of_employees:
                        message = f"Your plan allows you add up to {number_of_employees} employees. You have reached the limit."
                        flash(message, 'danger')
                        return redirect(url_for('my_employees.register_employees'))

                    register = Employee.add_employee(
                        db_session, company_id,
                        first_name, last_name, nid, rssb_number,
                        bank_name, bank_account, branch_name,
                        account_name, birth_date, marital_status, gender,
                        employee_tin, employee_type, department, net_salary,
                        transport_allowance, housing_allowance,
                        communication_allowance, over_time,
                        other_allowance, email, phone, job_title,
                        hire_date, annual_leave_balance, extra_leave_days,
                        gross_salary, total_staff_cost, contract_end_date
                    )
                    current_app.logger.info(f"Register: {register} and the type is {type(register)}")

                    current_app.logger.info("before accessing the if statement when an employee has been registered")
                    if register:
                        current_app.logger.info("after accessing the if statement when an employee has been registered")
                        message= f"""
                        Employee {first_name} {last_name}  was registered succesfully
                        """
                        flash(message, 'success')
                        current_app.logger.info(message)
                        # send the registered employee to the device database
                        try:
                            current_app.logger.info("before retriving employee_id from register")
                        #convert register to a dictionary
                            current_app.logger.info(f"Register: {register}")
                            employee_id = str(register.employee_id)
                            current_app.logger.info(f"Employee ID: {employee_id}")
                            name = f"{first_name} {last_name}"
                            current_app.logger.info(f"Name: {name}")
                            roll_id = 0
                            # construct the employee data as a json object
                            company_id = str(company_id)
                            # Check if the company with the company-id exists in the companies
                            url = f"{netpipo_base_url}/get_database_name_by_id"
                            data = {
                                'company_id': company_id
                            }
                            try:
                                response = requests.get(url, params=data)
                                database_name = response.json().get('database_name')
                                current_app.logger.info(f"Database name: {database_name}")
                            except Exception as e:
                                current_app.logger.error(f"Error fetching database name: {e}")
                                database_name = None
                            if database_name == None:
                                current_app.logger.error("Database name is missing")
                                flash(message, 'success')
                                return redirect(url_for('my_employees.register_employees'))
                            employee_data = {
                                'employee_id': employee_id,
                                'privilege': roll_id,
                                'name': name,
                                'company_id': company_id
                            }
                            current_app.logger.info(f"Employee data: {employee_data}")
                            # send the employee data to the device database
                            url = f"{netpipo_base_url}/addEmployee"
                            current_app.logger.info(f"Posting employee data: {employee_data} to {url}")

                            # Post employee data to the endpoint
                            try:
                                response = requests.post(url, json=employee_data)
                                current_app.logger.info(f"Response: {response}")
                                #response.raise_for_status()  # Raise an exception for HTTP errors
                                response_data = response.json()
                                current_app.logger.info(f"Response jsonified: {response_data}")
                            except requests.exceptions.RequestException as e:
                                current_app.logger.error(f"Error posting employee data: {e}")

                            user_id = response_data['userId']
                            # Send employee data to the device
                            url2 = f"{netpipo_base_url}/setOneUserJson"
                            # get the devices associated with the company given the companyu_id
                            url3 = f"{netpipo_base_url}/get_company_devices"
                            try:
                                response = requests.get(url3, params={'company_id': company_id})
                                current_app.logger.info(f"Response: {response}")
                                #response.raise_for_status()  # Raise an exception for HTTP errors
                                response_data = response.json()
                                devices = response_data.get('devices')
                                current_app.logger.info(f"Devices: {devices}")
                            except Exception as e:
                                current_app.logger.error(f"Error fetching devices: {e}")
                                devices = []
                            if len(devices) > 0:
                                for device_sn in devices:
                                    current_app.logger.info(f"Device SN: {device_sn}")
                                    payload = {
                                        "enrollId": user_id,
                                        "backupNum": -1,
                                        "deviceSn": device_sn
                                    }
                                    try:
                                        response2 = requests.post(url2, json=payload)
                                        #response2.raise_for_status()
                                        current_app.logger.info(f"employee sent to the device: {response2.json()}")
                                    except Exception as e:
                                        current_app.logger.error(f"Error sending data to device: {str(e)}")

                        except Exception as e:
                            current_app.logger.error(f"Error: {e}")
                        return redirect(url_for('my_employees.register_employees'))
                    else:
                        message = f"Error while saving employee: {register}"
                        flash(message, 'danger')
                        current_app.logger.error(message)
                        return redirect(url_for('my_employees.register_employees'))
            except Exception as e:
                current_app.logger.error(f"Error saving employee: {e}")
                flash('An error occurred. Please try again later.', 'danger')
                return render_template('employees/register_employees_v2.html', form=form)
        except Exception as e:
            current_app.logger.error(f"Error: {e}")
            flash('An error occurred. Please try again later.', 'danger')
            return redirect(url_for('admin_data.dashboard'))
    try:
        return render_template('employees/register_employees_v2.html', form=form)
    except Exception as e:
        current_app.logger.error(f"Error: {e}")
        return redirect(url_for('admin_data.dashboard'))


@my_employees_bp.route('/v2/employees_list', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def employees_list():
    """Get all employees in the company."""
    try:
        # Get the company_id from the session
        company_id = session.get('company_id')
        current_app.logger.info(f"Company ID: {company_id}")
        if not company_id:
            current_app.logger.error("Company ID is missing in session")
            flash('An error occurred. Please try again later.', 'danger')
            return redirect(url_for('admin_data.dashboard'))

        # Get the company database name
        database_name = CompanyHelpers.get_company_database_name(company_id)
        current_app.logger.info(f"Database name: {database_name}")
        if not database_name:
            current_app.logger.error("Database name could not be retrieved")
            flash('An error occurred. Please try again later.', 'danger')
            return redirect(url_for('admin_data.dashboard'))
        # Initialize the database connection and get all employees
        db_connection = DatabaseConnection()
        with db_connection.get_session(database_name) as db_session:
            employees = Employee.get_employees(db_session)
            current_app.logger.info(f"Employees: {employees}")
            if not employees:
                current_app.logger.error("No employees found")
                flash('No employees found.', 'danger')
        # Get the attendance_service status from the session
        attendance_service = session.get('attendance_service')
        print("attendance_service: ", attendance_service)
        current_app.logger.info(f"Attendance service status: {attendance_service}")
        return render_template('employees/employees_list_v2.html', employees=employees,
                               Auxillary=Auxillary,
                               attendance_service=attendance_service)
    except Exception as e:
        logging.error(f"Error: {e}")
        flash('An error occurred. Please try again later.', 'danger')
        return redirect(url_for('admin_data.dashboard'))

@my_employees_bp.route('/v2/get_inactive_employees', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def get_inactive_employees():
    """Get all inactive employees in the company."""
    try:
        # Get the company_id from the session
        company_id = session.get('company_id')
        if not company_id:
            current_app.logger.error("Company ID is missing in session")
            flash('An error occurred. Please try again later.', 'danger')
            return redirect(url_for('admin_data.dashboard'))

        # Get the company database name
        database_name = CompanyHelpers.get_company_database_name(company_id)
        if not database_name:
            current_app.logger.error("Database name could not be retrieved")
            flash('An error occurred. Please try again later.', 'danger')
            return redirect(url_for('admin_data.dashboard'))
        # Initialize the database connection and get all employees
        db_connection = DatabaseConnection()
        with db_connection.get_session(database_name) as db_session:
            employees = Employee.get_inactive_employees(db_session)
            if not employees:
                current_app.logger.error("No employees found")
                flash('No employees found.', 'danger')
        # Get the attendance_service status from the session
        attendance_service = session.get('attendance_service')
        current_app.logger.info(f"Attendance service status: {attendance_service}")
        return render_template('employees_list.html', employees=employees,
                               Auxillary=Auxillary,
                               attendance_service=attendance_service)
    except Exception as e:
        logging.error(f"Error: {e}")
        flash('An error occurred. Please try again later.', 'danger')
        return redirect(url_for('admin_data.dashboard'))

@my_employees_bp.route('/v2/register_employees_percentage', methods=['POST', 'GET'])
@role_required(['hr', 'accountant', 'manager', 'company_hr'])
def register_employees_percentage():
    """Register employees with percentage-based salary calculations."""
    form = EmployeePercentageForm()
    error_messages = []
    # Fetch employee types from the database
    try:
        employee_types = EmployeeType.get_employee_types()
        form.employee_type.choices = [ type['employee_type_name'] for type in employee_types]
    except Exception as e:
        current_app.logger.error(f"Error fetching employee types: {e}")
        employee_types = []
        form.employee_type.choices = []

    # Get the company_id from the session
    company_id = session.get('company_id')
    if not company_id:
        current_app.logger.error("Company ID is missing in session")
        flash('Company ID is missing in session.', 'danger')
        return redirect(url_for('admin_data.dashboard'))

    database_name = session.get('database_name')
    if not database_name:
        current_app.logger.error("Database name could not be retrieved")
        flash('Database name could not be retrieved.', 'danger')
        return redirect(url_for('admin_data.dashboard'))

    db_connection = DatabaseConnection()
    try:
        with db_connection.get_session(database_name) as db_session:
            # Retrieve Departments from the database
            try:
                departments = Departments.get_departments(db_session)
                form.department.choices = [('', 'Select Department')] + [
                    (department['department_name'], department['department_name']) for department in departments]
            except Exception as e:
                current_app.logger.error(f"Error fetching departments: {e}")
                form.department.choices = []

            if request.method == 'POST':
                data = request.form
                current_app.logger.info(f"Form data: {data}")

                # Validate form first
                if not form.validate_on_submit():
                    error_messages = []
                    for field, errors in form.errors.items():
                        for error in errors:
                            error_messages.append(f"{field}: {error}")
                    if error_messages:
                        flash(error_messages, 'danger')
                        return redirect(url_for('my_employees.register_employees_percentage'))

                # Extract form data and convert empty strings to None
                first_name = data.get('first_name') or None
                last_name = data.get('last_name') or None
                nid = data.get('nid') or None
                rssb_number = data.get('rssb_number') or None
                bank_name = data.get('bank_name') or None
                bank_account = data.get('bank_account') or None
                branch_name = data.get('branch_name') or None
                account_name = data.get('account_name') or None
                birth_date = data.get('birth_date') or None
                marital_status = data.get('marital_status') or None
                gender = data.get('gender') or None
                employee_tin = data.get('employee_tin') or None
                employee_type = data.get('employee_type') or None
                department = data.get('department') or None
                salary_type = data.get('salary_type') or None
                salary_amount = data.get('salary_amount') or None

                # Validate salary_amount is provided and is numeric
                if not salary_amount:
                    flash('Salary amount is required.', 'danger')
                    return redirect(url_for('my_employees.register_employees_percentage'))

                try:
                    salary_amount = Decimal(str(salary_amount).strip())
                    if salary_amount <= 0:
                        flash('Salary amount must be greater than zero.', 'danger')
                        return redirect(url_for('my_employees.register_employees_percentage'))
                except (ValueError, TypeError, InvalidOperation):
                    flash('Invalid salary amount. Please enter a valid number.', 'danger')
                    return redirect(url_for('my_employees.register_employees_percentage'))

                # Get salary calculation method and percentage fields (convert empty strings to None)
                salary_calculation_method = data.get('salary_calculation_method', 'percentage_based')
                basic_salary_percentage = data.get('basic_salary_percentage') or None
                transport_allowance_percentage = data.get('transport_allowance_percentage') or None
                housing_allowance_percentage = data.get('housing_allowance_percentage') or None
                communication_allowance_percentage = data.get('communication_allowance_percentage') or None

                # Other fields (convert empty strings to None)
                over_time = data.get('over_time') or None
                other_allowance = data.get('other_allowance') or None
                email = data.get('email') or None
                phone = data.get('phone') or None
                job_title = data.get('job_title') or None
                hire_date = data.get('hire_date') or None
                contract_end_date = data.get('contract_end_date') or None

                # Handle annual leave balance and extra leave days with proper error handling
                try:
                    annual_leave_balance_str = data.get("annual_leave_balance", "0").strip()
                    annual_leave_balance = Decimal(annual_leave_balance_str) if annual_leave_balance_str else Decimal('0')
                except (ValueError, TypeError, InvalidOperation):
                    annual_leave_balance = Decimal('0')

                try:
                    extra_leave_days_str = data.get("extra_leave_days", "0").strip()
                    extra_leave_days = Decimal(extra_leave_days_str) if extra_leave_days_str else Decimal('0')
                except (ValueError, TypeError, InvalidOperation):
                    extra_leave_days = Decimal('0')

                # Initialize calculated_gross_salary to avoid scope issues
                calculated_gross_salary = None

                # Validate that percentage-based method is being used
                if salary_calculation_method != 'percentage_based':
                    flash('This route is specifically for percentage-based salary calculations.', 'danger')
                    return redirect(url_for('my_employees.register_employees_percentage'))

                # Validate that gross or net salary is selected
                if salary_type not in ['gross_salary', 'net_salary']:
                    flash('Percentage-based calculation supports Gross Salary or Net Salary types only.', 'danger')
                    return redirect(url_for('my_employees.register_employees_percentage'))

                # Note: Empty strings are already converted to None during data extraction above

                # Validate and calculate percentage-based components using Decimal
                try:
                    percentages = {
                        'basic_salary_percentage': Decimal(str(basic_salary_percentage)) if basic_salary_percentage else None,
                        'transport_allowance_percentage': Decimal(str(transport_allowance_percentage)) if transport_allowance_percentage else None,
                        'housing_allowance_percentage': Decimal(str(housing_allowance_percentage)) if housing_allowance_percentage else None,
                        'communication_allowance_percentage': Decimal(str(communication_allowance_percentage)) if communication_allowance_percentage else None
                    }
                except (ValueError, TypeError, InvalidOperation) as e:
                    current_app.logger.error(f"Error converting percentages to Decimal: {e}")
                    flash('Invalid percentage values. Please enter valid numbers for percentages.', 'danger')
                    return redirect(url_for('my_employees.register_employees_percentage'))

                # Validate percentages
                is_valid, error_message = PercentageBasedSalaryCalculator.validate_percentages(percentages)
                if not is_valid:
                    flash(error_message, 'danger')
                    return redirect(url_for('my_employees.register_employees_percentage'))

                # Validate that basic salary percentage is provided for net salary calculations
                if salary_type == 'net_salary' and not percentages.get('basic_salary_percentage'):
                    flash('Basic salary percentage is required for net salary calculations.', 'danger')
                    return redirect(url_for('my_employees.register_employees_percentage'))

                # Process salary type and calculate components
                if salary_type == 'gross_salary':
                    gross_salary = salary_amount
                    net_salary = None
                    total_staff_cost = None

                    # Calculate components from gross salary
                    calculated_components = PercentageBasedSalaryCalculator.calculate_components_from_gross(
                        gross_salary, percentages
                    )

                elif salary_type == 'net_salary':
                    # For net salary input, we store the net salary and calculated allowances
                    net_salary = salary_amount  # Store the user-provided net salary
                    gross_salary = None  # Don't store calculated gross salary
                    total_staff_cost = None

                    try:
                        # Get company tax configuration
                        company_tax_config = PercentageBasedSalaryCalculator.get_company_tax_config(
                            db_session, employee_type
                        )

                        # Use goal seek to calculate gross and components from net salary
                        goal_seek_result = PercentageBasedSalaryCalculator.calculate_gross_from_net_percentage_based(
                            net_salary=Decimal(str(salary_amount)),  # Use salary_amount (the input net salary)
                            percentages=percentages,
                            company_tax_config=company_tax_config
                        )

                        # Extract calculated components (but not the gross salary)
                        calculated_gross_salary = goal_seek_result['gross_salary']  # For logging only
                        calculated_components = {
                            'basic_salary': goal_seek_result['basic_salary'],
                            'transport_allowance': goal_seek_result['transport_allowance'],
                            'housing_allowance': goal_seek_result['housing_allowance'],
                            'communication_allowance': goal_seek_result['communication_allowance']
                        }

                        current_app.logger.info(f"Goal seek completed. Input net: {salary_amount}, Calculated gross: {calculated_gross_salary}")
                        current_app.logger.info(f"Storing net_salary={net_salary}, calculated allowances, gross_salary=None")

                    except Exception as e:
                        current_app.logger.error(f"Error in goal seek calculation: {e}")
                        flash(f'Error calculating allowances from net salary: {str(e)}', 'danger')
                        return redirect(url_for('my_employees.register_employees_percentage'))

                # Set calculated values
                basic_salary = calculated_components.get('basic_salary')
                transport_allowance = calculated_components.get('transport_allowance') or 0
                housing_allowance = calculated_components.get('housing_allowance') or 0
                communication_allowance = calculated_components.get('communication_allowance') or 0

                current_app.logger.info(f"Percentage-based calculation: basic_salary={basic_salary}, "
                                       f"transport_allowance={transport_allowance}, housing_allowance={housing_allowance}, "
                                       f"communication_allowance={communication_allowance}")

                # Prepare complete employee data dictionary
                employee_data = {
                    # Personal Information
                    'company_id': company_id,
                    'first_name': first_name,
                    'last_name': last_name,
                    'nid': nid,
                    'nsf': rssb_number,
                    'bank_name': bank_name,
                    'bank_account': bank_account,
                    'branch_name': branch_name,
                    'account_name': account_name,
                    'birth_date': birth_date,
                    'marital_status': marital_status,
                    'gender': gender,
                    'employee_tin': employee_tin,
                    'employee_type': employee_type,
                    'department': department,
                    'email': email,
                    'phone': phone,
                    'job_title': job_title,
                    'hire_date': hire_date,
                    'annual_leave_balance': annual_leave_balance,
                    'extra_leave_days': extra_leave_days,
                    'contract_end_date': contract_end_date,

                    # Salary Information (calculated or provided)
                    'net_salary': net_salary,
                    'gross_salary': gross_salary,
                    'total_staff_cost': total_staff_cost,
                    'basic_salary': basic_salary,
                    'transport_allowance': transport_allowance,
                    'housing_allowance': housing_allowance,
                    'communication_allowance': communication_allowance,
                    'over_time': over_time,
                    'other_allowance': other_allowance,

                    # Percentage-based calculation metadata
                    'salary_calculation_method': salary_calculation_method,
                    'basic_salary_percentage': basic_salary_percentage,
                    'transport_allowance_percentage': transport_allowance_percentage,
                    'housing_allowance_percentage': housing_allowance_percentage,
                    'communication_allowance_percentage': communication_allowance_percentage,

                    # Input metadata for logging
                    'input_salary_type': salary_type,
                    'input_salary_amount': salary_amount,
                    'calculated_gross_salary': calculated_gross_salary
                }

                # Log complete employee data that will be saved
                current_app.logger.info(f"Complete employee data to be saved:")
                for key, value in employee_data.items():
                    current_app.logger.info(f"  - {key}: {value}")

                # Check company plan limits
                try:
                    plan_id = session.get('company_plan_id')
                    plan = Plans.get_plan_by_id(plan_id)
                    if plan:
                        number_of_employees = plan['num_of_employees']
                        all_employees = Employee.get_employees(db_session)
                        count_employees = len(all_employees)
                        if count_employees >= number_of_employees:
                            message = f"Your plan allows you add up to {number_of_employees} employees. You have reached the limit."
                            flash(message, 'danger')
                            return redirect(url_for('my_employees.register_employees_percentage'))
                except Exception as e:
                    current_app.logger.error(f"Error checking plan limits: {e}")

                try:
                    # Register the employee using the complete data dictionary
                    register = Employee.add_employee_percentage_from_dict(db_session, employee_data)

                    if register:
                        if employee_data['input_salary_type'] == 'net_salary':
                            # Clean and convert input amount to Decimal for formatting
                            input_amount_str = str(employee_data['input_salary_amount']).strip()
                            input_amount = Decimal(input_amount_str)
                            basic_salary_amount = Decimal(str(employee_data['basic_salary'])) if employee_data['basic_salary'] else Decimal('0')
                            transport_amount = Decimal(str(employee_data['transport_allowance'])) if employee_data['transport_allowance'] else Decimal('0')
                            housing_amount = Decimal(str(employee_data['housing_allowance'])) if employee_data['housing_allowance'] else Decimal('0')
                            # Convert Decimal to float only for string formatting
                            message = f"Employee {employee_data['first_name']} {employee_data['last_name']} was registered successfully. Net salary of {float(input_amount):,.0f} with calculated allowances: Basic {float(basic_salary_amount):,.0f}, Transport {float(transport_amount):,.0f}, Housing {float(housing_amount):,.0f}."
                        else:
                            message = f"Employee {employee_data['first_name']} {employee_data['last_name']} was registered successfully with percentage-based salary calculation."
                        flash(message, 'success')
                        return redirect(url_for('my_employees.employees_list'))
                    else:
                        flash('Failed to register employee.', 'danger')
                        return redirect(url_for('my_employees.register_employees_percentage'))

                except Exception as e:
                    db_session.rollback()
                    current_app.logger.error(f"Error registering employee: {e}")
                    flash('An error occurred while registering the employee.', 'danger')
                    return redirect(url_for('my_employees.register_employees_percentage'))

    except Exception as e:
        current_app.logger.error(f"Error in register_employees_percentage route: {e}")
        flash('An error occurred while accessing the employee registration page. Please try again later.', 'danger')
        return redirect(url_for('admin_data.dashboard'))

    try:
        return render_template('employees/register_employees_percentage_v2.html', form=form)
    except Exception as e:
        current_app.logger.error(f"Error: {e}")
        return redirect(url_for('admin_data.dashboard'))


@my_employees_bp.route('/v2/list_employees', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def list_employees():
    """Get all employees in the company."""
    MICROSERVICE_KEY = CompanyHelpers.get_company_api_key()
    BASE_URL = os.getenv('BASE_URL')

    try:
        database_name = session.get('database_name')
        if not database_name:
            current_app.logger.error("Database name could not be retrieved")
            flash('An error occurred. Please try again later.', 'danger')
            return redirect(url_for('admin_data.dashboard'))

        # Initialize employee information variable with an empty list
        employees_info = []

        # Initialize the database connection and get all employees
        db_connection = DatabaseConnection()
        with db_connection.get_session(database_name) as db_session:
            employees = Employee.get_employees(db_session)
            if not employees:
                current_app.logger.error("No employees found")
                flash('No employees found.', 'danger')

            try:
                images = Attendance.get_images_by_subject(MICROSERVICE_KEY, BASE_URL)
                current_app.logger.info('Images retrieved')

                # Create a mapping of employee IDs to image URLs
                image_map = {image['subject_name'][:36]: image['image_url'] for image in images}

                # Build employees_info with image URLs
                for employee in employees:
                    employee_data = {
                        'employee_id': employee['employee_id'],
                        'first_name': employee['first_name'],
                        'last_name': employee['last_name'],
                        'nid': employee['nid'],
                        'phone': employee['phone'],
                        'image_url': image_map.get(str(employee['employee_id']), None)  # Get image URL or None
                    }
                    employees_info.append(employee_data)

            except Exception as e:
                current_app.logger.error(f"Error fetching images: {e}")

        # Get the attendance_service status from the session
        attendance_service = session.get('attendance_service')
        current_app.logger.info(f"Attendance service status: {attendance_service}")

        return render_template('list_employees.html', employees=employees_info,
                               Auxillary=Auxillary,
                               attendance_service=attendance_service)
    except Exception as e:
        logging.error(f"Error: {e}")
        flash('An error occurred. Please try again later.', 'danger')
        return redirect(url_for('admin_data.dashboard'))

@my_employees_bp.route('/v2/employee_leave_balance', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def employee_leave_balance_list():
    """Get all employees in the company."""
    try:
        # Get the company_id from the session
        company_id = session.get('company_id')
        current_app.logger.info(f"Company ID: {company_id}")
        if not company_id:
            current_app.logger.error("Company ID is missing in session")
            flash('An error occurred. Please try again later.', 'danger')
            return redirect(url_for('admin_data.dashboard'))

        # Get the company database name
        database_name = CompanyHelpers.get_company_database_name(company_id)
        current_app.logger.info(f"Database name: {database_name}")
        if not database_name:
            current_app.logger.error("Database name could not be retrieved")
            flash('An error occurred. Please try again later.', 'danger')
            return redirect(url_for('admin_data.dashboard'))
        # Initialize the database connection and get all employees
        db_connection = DatabaseConnection()
        with db_connection.get_session(database_name) as db_session:
            employees = Employee.get_employees(db_session)
            employees_info = [
                    {
                        **employee,
                        "annual_leave_balance": round(employee.get("annual_leave_balance") or 0, 2)
                    }
                    for employee in employees
                ]
            current_app.logger.info(f"Employees: {employees}")
            if not employees:
                current_app.logger.error("No employees found")
                flash('No employees found.', 'danger')
        return render_template('employees/leave_balance.html', employees=employees_info, Auxillary=Auxillary)
    except Exception as e:
        logging.error(f"Error: {e}")
        flash('An error occurred. Please try again later.', 'danger')
        return redirect(url_for('admin_data.dashboard'))


@my_employees_bp.route('/v2/update_employee/<uuid:employee_id>', methods=['POST', 'GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def update_employee(employee_id):
    """Update an employee's details."""
    try:
        # Get the company_id from the session
        company_id = session.get('company_id')
        if not company_id:
            logging.error("Company ID is missing in session")
            flash('Company ID is missing in session.', 'danger')
            return redirect(url_for('admin_data.dashboard'))

        # Get the company database name
        database_name = CompanyHelpers.get_company_database_name(company_id)
        if not database_name:
            logging.error("Database name could not be retrieved")
            flash('Database name could not be retrieved.', 'danger')
            return redirect(url_for('admin_data.dashboard'))

        # Initialize the database connection and get the employee
        db_connection = DatabaseConnection()
        with db_connection.get_session(database_name) as db_session:
            employee = Employee.get_employee_by_id(db_session, employee_id)
            if not employee:
                logging.error("Employee not found")
                flash('Employee not found.', 'danger')
                return redirect(url_for('admin_data.dashboard'))

            # Create the form instance
            form = EmployeeUpdateForm(obj=employee)

            # Fetch employee types from the database
            try:
                employee_types = EmployeeType.get_employee_types()
                form.employee_type.choices = [(type['employee_type_name'], type['employee_type_name']) for type in employee_types]
            except Exception as e:
                current_app.logger.error(f"Error fetching employee types: {e}")
                form.employee_type.choices = []
            try:
                # Retrieve Departments from the database
                departments = Departments.get_departments(db_session)
                form.department.choices = [('', 'Select Department')] + [
                    (department['department_name'], department['department_name']) for department in departments]
            except Exception as e:
                current_app.logger.error(f"Error fetching departments: {e}")
                form.department.choices = []

            # Retrieve Sites from the database
            try:
                sites = Site.get_sites(db_session)
                current_app.logger.info('Sites: %s', sites)
            except Exception as e:
                current_app.logger.error(f"Error fetching sites: {e}")
                sites = []

            if request.method == 'POST':
                data = request.form

                # log the form data
                current_app.logger.info(f"Form data: {data}")
                # Validate the form data
                if not form.validate_on_submit():
                    error_messages = []
                    for field, errors in form.errors.items():
                        error_messages.append(f"{field}: {errors}")
                    for error in error_messages:
                        flash(error, 'danger')
                    return redirect(url_for('my_employees.update_employee', employee_id=employee_id))
                # Get the site_id from the form and validate it
                site_id = request.form.get('site_id')

                # convert the site_id to UUID
                try:
                    if site_id:
                        site_id = UUID(site_id)
                        current_app.logger.info(f'site id: {site_id}')
                except Exception as e:
                    current_app.loggger.error(f"an error occured: {str(e)}")
                    flash('Invalid site ID.', 'danger')
                    return redirect(url_for('my_employees.update_employee', employee_id=employee_id))


                # Update the employee details
                try:
                    first_name = request.form.get('first_name') or None
                    last_name = request.form.get('last_name') or None
                    nid = request.form.get('nid') or None
                    nsf = request.form.get('nsf') or None
                    bank_name = request.form.get('bank_name') or None
                    bank_account = request.form.get('bank_account') or None
                    branch_name = request.form.get('branch_name') or None
                    account_name = request.form.get('account_name') or None
                    birth_date = request.form.get('birth_date') or None
                    marital_status = request.form.get('marital_status') or None
                    gender = request.form.get('gender') or None
                    employee_tin = request.form.get('employee_tin') or None
                    employee_type = request.form.get('employee_type') or None
                    department = request.form.get('department') or None
                    salary_type = request.form.get('salary_type') or None
                    salary_amount = request.form.get('salary_amount') or None

                    transport_allowance = request.form.get('transport_allowance') or None
                    housing_allowance = request.form.get('housing_allowance') or None
                    communication_allowance = request.form.get('communication_allowance') or None
                    over_time = request.form.get('over_time') or None
                    other_allowance = request.form.get('other_allowance') or None
                    email = request.form.get('email') or None
                    phone = request.form.get('phone') or None
                    job_title = request.form.get('job_title') or None
                    current_app.logger.info(f"job_title: {job_title}")
                    hire_date = request.form.get('hire_date') or None
                    contract_end_date = request.form.get('contract_end_date') or None

                    # Handle annual leave balance (Numeric) and extra leave days (Integer) with proper error handling
                    try:
                        annual_leave_balance_str = data.get("annual_leave_balance", "0").strip()
                        annual_leave_balance = Decimal(annual_leave_balance_str) if annual_leave_balance_str else Decimal('0')
                    except (ValueError, TypeError, InvalidOperation):
                        annual_leave_balance = Decimal('0')

                    try:
                        extra_leave_days_str = data.get("extra_leave_days", "0").strip()
                        extra_leave_days = int(extra_leave_days_str) if extra_leave_days_str else 0
                    except (ValueError, TypeError):
                        extra_leave_days = 0
                    current_app.logger.info(f"hire_date: {hire_date}")
                    if hire_date:
                        try:
                            hire_date = datetime.strptime(hire_date, '%Y-%m-%d')  # Adjust format to match your input (e.g., '2024-11-22').
                        except Exception as e:
                            current_app.logger.error(f"Error converting hire date: {e}")
                            hire_date = None
                    if birth_date:
                        try:
                            birth_date = datetime.strptime(birth_date, '%Y-%m-%d')  # Adjust format to match your input (e.g., '2024-11-22').
                        except Exception as e:
                            current_app.logger.error(f"Error converting hire date: {e}")
                            birth_date = None
                    if site_id:
                        site_id = site_id
                    else:
                        site_id = None
                    is_active = request.form.get('is_active')
                    if is_active == 'no':
                        message_2 = f"{employee['first_name']} is Inactive"

                    else:
                        message_2 = f"{employee['first_name']} is Active"
                    print(message_2)
                    is_brd_sponsored = request.form.get('is_brd_sponsored')
                    attendance_applicable = request.form.get('attendance_applicable')
                    if salary_type == 'net_salary':
                        # set the salary amount depending on the salary type
                        net_salary= salary_amount
                        gross_salary = None
                        total_staff_cost = None
                    elif salary_type == 'gross_salary':
                        gross_salary = salary_amount
                        net_salary = None
                        total_staff_cost = None
                    elif salary_type == 'total_staff_cost':
                        total_staff_cost = salary_amount
                        gross_salary = None
                        net_salary = None
                    if not employee_tin:
                        employee_tin = *********

                    # We wanna make sure the user does not activate employees beyond their allowed plan limits
                    if is_active == 'yes':
                        #Check the company plan and the number of employees the plan allows
                        current_app.logger.info(f"Checking company plan limits for employee {employee['first_name']}")
                        plan_id = session.get('company_plan_id')
                        plan = Plans.get_plan_by_id(plan_id)
                        if plan:
                            number_of_employees = plan['num_of_employees']
                            all_employees = Employee.get_employees(db_session)
                            count_employees = len(all_employees)
                            if count_employees >= number_of_employees:
                                flash(f"You have reached the maximum number of employees allowed by your plan ({number_of_employees}). Please upgrade your plan to add more employees.", 'danger')
                                return redirect(url_for('my_employees.employees_list'))
                        else:
                            flash('Your company plan does not allow adding more employees. Please contact support.', 'danger')
                            return redirect(url_for('my_employees.employees_list'))

                    try:
                        result = Employee.update_employee(
                            db_session, employee_id, first_name,
                            last_name, nid, nsf, bank_name, bank_account, branch_name, account_name,
                            birth_date, marital_status, gender, employee_tin, employee_type, department,
                            net_salary, gross_salary, total_staff_cost, transport_allowance, housing_allowance,
                            communication_allowance, over_time, other_allowance, email, phone, job_title,
                            hire_date, site_id, is_active, is_brd_sponsored, attendance_applicable,
                            annual_leave_balance, extra_leave_days, contract_end_date
                        )

                        current_app.logger.info(f"Result: {result}")
                        current_app.logger.info(f"Result: {result}")
                        message = f"Employee {employee['first_name']} {employee['last_name']} was updated successfully."
                        flash(message, 'success')
                        return redirect(url_for('my_employees.employees_list'))
                    except Exception as e:
                        db_session.rollback()
                        logging.error(f"Error updating employee: {e}")
                        flash('An error occurred while updating the employee. Please try again later.', 'danger')
                        return redirect(url_for('my_employees.update_employee', employee_id=employee_id))

                except Exception as e:
                    db_session.rollback()
                    logging.error(f"Error updating employee: {e}")
                    flash('An error occurred while updating the employee. Please try again later.', 'danger')
                    return redirect(url_for('my_employees.update_employee', employee_id=employee_id))

            # For GET requests, render the form with the employee's data
            try:
                form.employee_type.data = employee['employee_type']
                form.department.data = employee['department']
                form.site_id.data = employee['site_id']
                form.nid.data = employee['nid']
                form.first_name.data = employee['first_name']
                form.last_name.data = employee['last_name']
                form.nsf.data = employee['nsf']
                form.bank_name.data = employee['bank_name']
                form.bank_account.data = employee['bank_account']
                form.branch_name.data = employee['branch_name']
                form.account_name.data = employee['account_name']
                if isinstance(employee['birth_date'], str):
                    # Convert string to datetime
                    current_app.logger.info(f"Employee hire date: {employee['hire_date']}")
                    form.birth_date.data = datetime.strptime(employee['birth_date'], '%d/%m/%Y')
                else:
                    current_app.logger.info(f"Employee hire date: {employee['hire_date']}")
                    form.birth_date.data = employee['birth_date']
                #Include the contract end date
                if isinstance(employee['contract_end_date'], str):
                    # Convert string to datetime
                    current_app.logger.info(f"Employee contract end date: {employee['contract_end_date']}")
                    form.contract_end_date.data = datetime.strptime(employee['contract_end_date'], '%d/%m/%Y')
                else:
                    current_app.logger.info(f"Employee contract end date: {employee['contract_end_date']}")
                    form.contract_end_date.data = employee['contract_end_date']
                form.marital_status.data = employee['marital_status']
                form.gender.data = employee['gender']
                form.employee_tin.data = employee['employee_tin']
                #form.salary_type.data = employee['salary_type']
                if employee['net_salary']:
                    form.salary_type.data = 'net_salary'
                    form.salary_amount.data = employee['net_salary']
                elif employee['gross_salary']:
                    form.salary_type.data = 'gross_salary'
                    form.salary_amount.data = employee['gross_salary']
                elif employee['total_staff_cost']:
                    form.salary_type.data = 'total_staff_cost'
                    form.salary_amount.data = employee['total_staff_cost']
                form.transport_allowance.data = employee['transport_allowance']
                form.housing_allowance.data = employee['housing_allowance']
                form.communication_allowance.data = employee['communication_allowance']
                form.over_time.data = employee['over_time']
                form.other_allowance.data = employee['other_allowance']
                form.email.data = employee['email']
                form.phone.data = employee['phone']
                form.job_title.data = employee['job_title']
                current_app.logger.info(f"Job Title: {employee['job_title']}")
                if isinstance(employee['hire_date'], str):
                    # Convert string to datetime
                    current_app.logger.info(f"Employee hire date: {employee['hire_date']}")
                    form.hire_date.data = datetime.strptime(employee['hire_date'], '%d/%m/%Y')
                else:
                    current_app.logger.info(f"Employee hire date: {employee['hire_date']}")
                    form.hire_date.data = employee['hire_date']
                form.is_active.data = employee['is_active']
                form.is_brd_sponsored.data = employee['is_brd_sponsored']
                form.attendance_applicable.data = employee['attendance_applicable']
                form.annual_leave_balance.data = employee.get('annual_leave_balance') if employee.get('annual_leave_balance') else 0
                form.extra_leave_days.data = employee.get('extra_leave_days') if employee.get('extra_leave_days') else 0

            except Exception as e:
                logging.error(f"Error fetching employee details: {e}")
                flash('Error fetching employee details.', 'danger')
                return redirect(url_for('admin_data.dashboard'))

            return render_template('employees/update_employee_v2.html', form=form, sites=sites, employee=employee)
    except Exception as e:
        logging.error(f"Error: {e}")
        flash('An unexpected error occurred.', 'danger')
        return redirect(url_for('admin_data.dashboard'))

@my_employees_bp.route('/v2/upload_employees', methods=['POST', 'GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def upload_employees():
    """Upload employees from an Excel file to the database."""
    form = UploadEmployeeForm()
    if request.method == 'POST':
        if not form.validate_on_submit():
            current_app.logger.error("Form validation failed")
            flash('Form validation failed.', 'danger')
            return redirect(url_for('my_employees.upload_employees'))

        company_id = session.get('company_id')
        if not company_id:
            logging.error("Company ID is missing in session")
            flash('First error occurred. Please try again later.', 'danger')
            return redirect(url_for('admin_data.dashboard'))

        database_name = session.get('database_name')
        if not database_name:
            logging.error("Database name could not be retrieved")
            flash('Second error occurred. Please try again later.', 'danger')
            return redirect(url_for('admin_data.dashboard'))

        db_connection = DatabaseConnection()
        try:
            with db_connection.get_session(database_name) as db_session:
                file = form.file.data
                import pandas as pd

                employees_df = pd.read_excel(file)
                count = 0
                addition = False
                for idx, row in employees_df.iterrows():
                    if pd.isna(row['First Name']) and pd.isna(row['Last Name']):
                        message = f"Row {idx+2}: Missing both First Name and Last Name."
                        #flash(f"Row {idx+1}: Missing both First Name and Last Name.", 'danger')
                        flash(message, 'danger')

                    first_name = row['First Name']
                    last_name = row['Last Name']
                    nid = str(row['nid']).split('.')[0]
                    rssb_number = str(row['nsf']).split('.')[0]
                    # If Bank Name row fField is empty, set it to empty string
                    if pd.isna(row['Bank Name']):
                        bank_name = None
                    else:
                        bank_name = row['Bank Name']

                    # If Account Number row field is empty, set it to empty string
                    if pd.isna(row['Account Number']):
                        account_number = None
                    else:
                        account_number = str(row['Account Number']).split('.')[0]

                    # If Branch Name row field is empty, set it to empty string
                    if pd.isna(row['Branch Name']):
                        branch_name = None
                    else:
                        branch_name = row.get('Branch Name', '')

                    # If Account Name row field is empty, set it to empty string
                    if pd.isna(row['Account Name']):
                        account_name = None
                    else:
                        account_name = row.get('Account Name', '')

                    # if Birth Date row field is empty, set it to None
                    if pd.isna(row['birth date']):
                        birth_date = None
                    else:
                        birth_date = pd.to_datetime(row['birth date'], errors='coerce') #
                    # If Marital Status row field is empty, set it to None
                    if pd.isna(row['marital status']):
                        marital_status = None
                    else:
                        marital_status = row['marital status']
                    gender = row['gender']
                    employee_tin = str(row['employee_tin']).split('.')[0]
                    employee_type = row['employee_type']

                    # If Department row field is empty, set it to None
                    if pd.isna(row['department']):
                        department = None
                    else:
                        department = row['department']
                    salary_type = row['Salary Type']
                    salary_amount = row['Salary Amount']
                    transport_allowance = row['transport_allowance'] if not pd.isna(row['transport_allowance']) else 0
                    housing_allowance = row['housing_allowance'] if not pd.isna(row['housing_allowance']) else 0
                    communication_allowance = row['communication_allowance'] if not pd.isna(row['communication_allowance']) else 0
                    overtime = row['overtime'] if not pd.isna(row['overtime']) else 0
                    other_allowances = row['other_allowances'] if not pd.isna(row['other_allowances']) else 0
                    # If Email row field is empty, set it to none
                    if pd.isna(row['email']):
                        email = None
                    else:
                        email = row['email']
                    # if Phone row field is empty, set it to None
                    if pd.isna(row['phone']):
                        phone = None
                    else:
                        phone = str(row['phone']).split('.')[0]
                    # if Job Title row field is empty, set it to None
                    if pd.isna(row['job_title']):
                        job_title = None
                    else:
                        job_title = row['job_title']

                    # if Hire Date row field is empty, set it to None
                    if pd.isna(row['hire_date']):
                        hire_date = None
                    else:
                        hire_date = pd.to_datetime(row['hire_date'], errors='coerce')
                    check1 = (salary_type == 'net salary')
                    check2 = (salary_type == 'gross salary')
                    current_app.logger.info(f"Check1: {check1}")
                    current_app.logger.info(f"Check2: {check2}")
                    if salary_type == 'net salary':
                        net_salary = salary_amount
                        gross_salary = None
                        total_staff_cost = None
                    elif salary_type == 'gross salary':
                        gross_salary = salary_amount
                        net_salary = None
                        total_staff_cost = None
                    elif salary_type == 'total staff cost':
                        total_staff_cost = salary_amount
                        gross_salary = None
                        net_salary = None
                    new_employee = Employee(
                        first_name=first_name,
                        last_name=last_name,
                        nid=nid,
                        nsf=rssb_number,
                        bank_name=bank_name,
                        bank_account=account_number,
                        branch_name=branch_name,
                        account_name=account_name,
                        birth_date=birth_date,
                        marital_status=marital_status,
                        gender=gender,
                        employee_tin=employee_tin,
                        employee_type=employee_type,
                        department=department,
                        net_salary=net_salary,
                        gross_salary=gross_salary,
                        total_staff_cost=total_staff_cost,
                        transport_allowance=transport_allowance,
                        housing_allowance=housing_allowance,
                        communication_allowance=communication_allowance,
                        over_time=overtime,
                        other_allowance=other_allowances,
                        email=email,
                        phone=phone,
                        job_title=job_title,
                        hire_date=hire_date
                    )
                     # Get the companies plan from session
                    plan_id = session.get('company_plan_id')
                    current_app.logger.info(f"Plan ID: {plan_id}")

                    # Check the plan details
                    try:
                        plan = Plans.get_plan_by_id(plan_id)
                        current_app.logger.info(f"Plan: {plan}")
                        if not plan:
                            current_app.logger.error("No plan found")

                        number_of_employees = plan['num_of_employees']
                        current_app.logger.info(f"Number of employees: {number_of_employees}")
                        current_app.logger.info(f"Type of number of employees: {type(number_of_employees)}")
                    except Exception as e:
                        current_app.logger.error(f"Error fetching plan: {e}")
                        number_of_employees = 0

                    all_employees = Employee.get_employees(db_session)
                    current_app.logger.info(f"Type of all employees: {type(all_employees)}")
                    current_app.logger.info(f"all employees: {all_employees}")
                    count_employees = len(all_employees)
                    if count_employees >= number_of_employees:
                        message = f"Error in Row {idx+2}: Your plan allows you add up to {number_of_employees} employees. You have reached the limit."
                        #flash(message, 'danger')
                        flash(message, 'danger')
                        continue

                    # Check for mandatory fields
                    mandatory = Employee.check_mandatory(db_session, new_employee)
                    if mandatory:
                        #flash(f"Error in Row {idx+1}: {mandatory}", 'danger')
                        message = f"Error in Row {idx+2}: {mandatory}"
                        flash(message, 'danger')
                        continue

                    # Check for unique fields
                    unique = Employee.check_unique(db_session, nid, rssb_number, email, phone)
                    if unique:
                        message = f"Error in Row {idx+2}: {unique}"
                        #flash(f"Error in Row {idx+1}: {unique}", 'danger')
                        flash(message, 'danger')
                        continue

                    # Check if the department is available
                    if department and not pd.isna(department):
                        departments = Departments.get_departments(db_session)
                        department_names = [dept['department_name'] for dept in departments]
                        available = Employee.check_if_department_available(db_session, department.strip().lower())
                        if not available:
                            message = f"""
                            Error in Row {idx+2}: Department {department} in your Excel file does not exist in your company
                            departments. If it is a new department, Kindly add it.
                            """

                            message = f"Error in Row {idx+2}: Department {department} in your Excel file does not exist in your company departments. your departments: {department_names}"
                            flash(message, 'danger')
                            continue
                    # Make sure the salary amount is a number and a positive number
                    try:
                        salary_amount = float(salary_amount)
                        if salary_amount < 0:
                            message = f"Error in Row {idx+2}: Salary amount must be a positive number."
                            flash(message, 'danger')
                            continue
                    except Exception as e:
                        message = f"Error in Row {idx+2}: Salary amount must be a number."
                        flash(message, 'danger')
                        continue

                    if salary_type == 'net salary':
                        net_salary = salary_amount
                        gross_salary = None
                        total_staff_cost = None
                    elif salary_type == 'gross salary':
                        gross_salary = salary_amount
                        net_salary = None
                        total_staff_cost = None
                    elif salary_type == 'total staff cost':
                        total_staff_cost = salary_amount
                        gross_salary = None
                        net_salary = None
                    else:
                        message = f"Error in Row {idx+2}: Salary type must be either 'Net Salary' or 'Gross Salary' or 'Total Staff Cost'."
                        flash(message, 'danger')
                        continue
                    try:
                        # Set default values for annual leave balance and extra leave days if not provided
                        annual_leave_balance = 0  # Default value
                        extra_leave_days = 0      # Default value

                        addition = Employee.add_employee(db_session, company_id, first_name, last_name, nid,
                                            rssb_number, bank_name, account_number, branch_name, account_name,
                                            birth_date, marital_status, gender,
                                            employee_tin, employee_type, department, net_salary,
                                            transport_allowance, housing_allowance,
                                            communication_allowance, overtime, other_allowances,
                                            email, phone, job_title, hire_date, annual_leave_balance, extra_leave_days,
                                            gross_salary, total_staff_cost)
                        count += 1
                        message=(f"*Employee {first_name} {last_name}'s data were uploaded successfully.*")
                        current_app.logger.info(message)
                        flash(message, 'success')

                    except Exception as e:
                        current_app.logger.error(f"Row {idx+2}: Error saving employee: {e}")
                        message = f"Error in Row {idx+2}: Third error occurred. Please try again later."
                        flash(message, 'danger')
                        continue


                if addition:
                    message = f"{count} Employee(s) were uploaded successfully.*"
                    flash(message, 'success')
                    return redirect(url_for('my_employees.employees_list'))
                else:
                    message = "No employees were uploaded, check the file and try again."
                    flash(message, 'danger')
                    return redirect(url_for('my_employees.upload_employees'))
        except Exception as e:
            current_app.logger.error(f"Error saving employees: {e}")
            logging.error("".join(traceback.format_exc()))
            flash('An error occurred. Please try again later.', 'danger')
            return redirect(url_for('admin_data.dashboard'))

    try:
        return render_template('employees/upload_employees_v2.html', form=form)
    except Exception as e:
        current_app.logger.error(f"Error: {e}")
        flash('error occurred. Please try again later.', 'danger')
        return redirect(url_for('admin_data.dashboard'))


@my_employees_bp.route('/v2/download_employees_template', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def download_employees_template():
    """Download an Excel template for uploading employees.
    Description:
    The template is located in the static folder and is named employees_template.xlsx.
    we want the user to download this template so that they can fill in the employee details
    """
    try:
        # Load the Excel template
        template_path = 'app/routes/employees/Employees data template Sample.xlsx'
        wb = load_workbook(template_path)
        # No need to access the active worksheet since we're just saving the workbook as is

        # Download the template available in the path.
        output = BytesIO()
        wb.save(output)
        output.seek(0)
        return send_file(output, as_attachment=True, download_name='sample_employees_data_template.xlsx')
    except Exception as e:
        current_app.logger.error(f"Error: {e}")
        flash('An error occurred. Please try again later.', 'danger')
        return redirect(url_for('admin_data.dashboard'))

@my_employees_bp.route('/v2/employee_payslip', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def employee_payslip():
    """Generate a payslip for all employees.
    """
    current_date = session.get('pay_date')
    if not current_date:
        current_date = datetime.now()
        current_app.logger.error("Current date is missing.")
    first_day = current_date.replace(day=1)
    _, days_in_month = calendar.monthrange(current_date.year, current_date.month)
    last_day = (first_day + timedelta(days=32)).replace(day=1) - timedelta(days=1)

    # Format dates as DD/MM/YYYY
    formatted_first_day = first_day.strftime("%d/%m/%Y")
    formatted_last_day = last_day.strftime("%d/%m/%Y")
    try:
        employees_with_deductions = session.get('employees_with_deductions')
        current_app.logger.info("Information found in session with employee deductions.")
        for data in employees_with_deductions:
            print(data)
            print("\n")

        if not employees_with_deductions:
            flash("No payroll summary data available.", 'danger')
            current_app.logger.error("No payroll summary data available.")
            return redirect(url_for('admin_data.dashboard'))

    except Exception as e:
        current_app.logger.error(f"An error occurred while getting payroll summary data: {e}")
        flash(f"An error occurred while getting payroll summary data", 'danger')
        return redirect(url_for('admin_data.dashboard'))

    try:
        # Get company data from session
        company_id = session.get('company_id')
        company_data = Company.get_company_by_id(company_id)
        first_name = session.get('first_name')
        last_name = session.get('last_name')
        pay_date = session.get('pay_date')
        if not pay_date:
            pay_date = current_date
            current_app.logger.error("Pay date is missing.")
        current_app.logger.info("Company data retrieved.")
    except Exception as e:
        current_app.logger.error(f"An error occurred while getting company data: {e}")
        company_data = None
    try:
        return render_template('employees/employee_payslip_v2.html',
                            employees_with_deductions=employees_with_deductions,
                            first_day=formatted_first_day,
                            last_day=formatted_last_day,
                            days_in_month=days_in_month,
                            pay_date=pay_date,
                            first_name=first_name.upper(),
                            last_name=last_name.upper(),
                            company_data=company_data, Auxillary=Auxillary)
    except Exception as e:
        current_app.logger.error(f"An error occurred while rendering employee payslip: {e}")
        flash("An error occurred while rendering employee payslip.", 'danger')
        return redirect(url_for('admin_data.dashboard'))

@my_employees_bp.route('/v2/single_employee_payslip/<uuid:employee_id>', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def single_employee_payslip(employee_id):
    """Generate a payslip for a single employee."""
    try:
        # Get pay_date from session
        pay_date = session.get('pay_date')
        current_app.logger.info(f"Raw pay_date from session: {pay_date} and type: {type(pay_date)}")

        # Normalize pay_date immediately
        if isinstance(pay_date, str):
            try:
                pay_date = datetime.strptime(pay_date, "%Y-%m-%d")
            except ValueError:
                current_app.logger.error("Invalid pay_date format from session. Using now().")
                pay_date = datetime.now()
        elif isinstance(pay_date, date):
            pay_date = datetime.combine(pay_date, datetime.min.time())
        elif not pay_date:
            pay_date = datetime.now()
            current_app.logger.error("Pay date is missing. Using now().")

        # Log final normalized type
        current_app.logger.info(f"Normalized pay_date: {pay_date}, type: {type(pay_date)}")

        # Calculate date range
        first_day = pay_date.replace(day=1)
        _, days_in_month = calendar.monthrange(pay_date.year, pay_date.month)
        last_day = (first_day + timedelta(days=32)).replace(day=1) - timedelta(days=1)

        formatted_first_day = first_day.strftime("%d/%m/%Y")
        formatted_last_day = last_day.strftime("%d/%m/%Y")

    except Exception as e:
        current_app.logger.error(f"Error while calculating date range: {e}")
        flash("An error occurred while getting current date.", 'danger')
        return redirect(url_for('admin_data.dashboard'))

    employees_data = session.get('employees_with_deductions')
    if not employees_data:
        flash("No payroll summary data available.", 'danger')
        current_app.logger.error("No payroll summary data available.")
        return redirect(url_for('admin_data.dashboard'))

    try:
        company_id = session.get('company_id')
        company_data = Company.get_company_by_id(company_id)
        logo = company_data['logo']
        first_name = session.get('first_name')
        last_name = session.get('last_name')
        current_app.logger.info("Company data retrieved.")
    except Exception as e:
        current_app.logger.error(f"An error occurred while getting company data: {e}")
        company_data = None

    # Find the employee in session data
    filtered_employee = next((item for item in employees_data if item['employee']['employee_id'] == employee_id), None)

    if filtered_employee:
        current_app.logger.info(f"hire_date type: {type(filtered_employee['employee']['hire_date'])}; value: {filtered_employee['employee']['hire_date']}")
    else:
        current_app.logger.error("Employee not found in session data")

    if not filtered_employee:
        flash("Employee not found.", 'danger')
        current_app.logger.error("Employee not found.")
        return redirect(url_for('admin_data.dashboard'))
    
    try:
        formatted_pay_date = pay_date.strftime('%B %Y').upper()
    except Exception as e:
        current_app.logger.error(f"An error occurred while formatting dates: {e}")
        flash("An error occurred while formatting dates.", 'danger')
        return redirect(url_for('admin_data.dashboard'))


    try:
        current_app.logger.info(f"Rendering single employee payslip for: {filtered_employee['employee']['first_name']}")

        return render_template('employees/single_employee_payslip.html',
                               emp_data=filtered_employee,
                               first_name=first_name.upper(),
                               last_name=last_name.upper(),
                               days_in_month=days_in_month,
                               last_day=formatted_last_day,
                               first_day=formatted_first_day,
                               company_data=company_data,
                               Auxillary=Auxillary,
                               logo=logo,
                               pay_date=formatted_pay_date)
    except Exception as e:
        current_app.logger.error(f"An error occurred while rendering single employee payslip: {e}")
        flash("An error occurred while rendering single employee payslip.", 'danger')
        return redirect(url_for('admin_data.dashboard'))


@my_employees_bp.route('/v2/send_payslip_email/<uuid:employee_id>', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def send_payslip_email(employee_id):
    """Send a payslip as a PDF attachment to an employee.
    """
    try:
        current_date = session.get('pay_date')
        if not current_date:
            current_date = datetime.now()
            current_app.logger.error("Current date is missing.")
        first_day = current_date.replace(day=1)
        _, days_in_month = calendar.monthrange(current_date.year, current_date.month)
        last_day = (first_day + timedelta(days=32)).replace(day=1) - timedelta(days=1)

        # Format dates as DD/MM/YYYY
        formatted_first_day = first_day.strftime("%d/%m/%Y")
        formatted_last_day = last_day.strftime("%d/%m/%Y")

    except Exception as e:
        current_app.logger.error(f"An error occurred while getting current date: {e}")
        flash("An error occurred while getting current date.", 'danger')
        return redirect(url_for('admin_data.dashboard'))

    employees_data = session.get('employees_with_deductions')
    if not employees_data:
        flash("No payroll summary data available.", 'danger')
        current_app.logger.error("No payroll summary data available.")
        return redirect(url_for('admin_data.dashboard'))

    try:
        company_id = session.get('company_id')
        company_data = Company.get_company_by_id(company_id)
        first_name = session.get('first_name')
        last_name = session.get('last_name')
        pay_date = session.get('pay_date')
        if not pay_date:
            pay_date = current_date
            current_app.logger.error("Pay date is missing.")
        current_app.logger.info("Company data retrieved.")

    except Exception as e:
        current_app.logger.error(f"An error occurred while getting company data: {e}")
        company_data = None
        flash("An error occurred while getting company data.", 'danger')
        return redirect(url_for('admin_data.dashboard'))

    # Get deductions for a specific employee
    filtered_employee = next((item for item in employees_data if item['employee']['employee_id'] == employee_id), None)
    if not filtered_employee:
        flash("Employee not found.", 'danger')
        current_app.logger.error("Employee not found.")
        return redirect(url_for('admin_data.dashboard'))

    # Check if employee has an email
    employee_email = filtered_employee['employee'].get('email')
    if not employee_email:
        flash("Employee does not have an email address. Please update the employee profile.", 'danger')
        current_app.logger.error("Employee does not have an email address.")
        return redirect(url_for('employees.single_employee_payslip', employee_id=employee_id))

    try:
        # Generate comprehensive PDF payslip without using number_to_words
        try:
            # Log company data for debugging
            current_app.logger.info(f"Company data for PDF: {company_data}")
            if 'logo' in company_data:
                current_app.logger.info(f"Company logo found: {company_data['logo']}")
            else:
                current_app.logger.warning("No logo found in company data")

            # Use the PDFGenerator class but with a modified version that doesn't use number_to_words
            prepared_by = f"{first_name.upper()} {last_name.upper()}"
            pdf_buffer = PDFGenerator.generate_payslip_pdf(
                filtered_employee,
                company_data,
                pay_date,
                formatted_first_day,
                formatted_last_day,
                days_in_month,
                prepared_by
            )
            current_app.logger.info("Comprehensive PDF generated successfully")
        except Exception as e:
            current_app.logger.error(f"Error generating comprehensive PDF: {e}")
            # Create a simple fallback PDF
            from reportlab.lib.pagesizes import A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
            from reportlab.lib.styles import getSampleStyleSheet
            from reportlab.lib.units import inch
            from io import BytesIO

            pdf_buffer = BytesIO()
            doc = SimpleDocTemplate(pdf_buffer, pagesize=A4)
            styles = getSampleStyleSheet()

            elements = []
            elements.append(Paragraph("PAYSLIP", styles['Title']))
            elements.append(Spacer(1, 0.2*inch))
            elements.append(Paragraph(f"Company: {company_data.get('company_name', '')}", styles['Normal']))
            elements.append(Paragraph(f"Employee: {filtered_employee['employee'].get('first_name', '')} {filtered_employee['employee'].get('last_name', '')}", styles['Normal']))
            elements.append(Paragraph(f"Period: {formatted_first_day} to {formatted_last_day}", styles['Normal']))

            doc.build(elements)
            pdf_buffer.seek(0)

        # Create a custom filename with employee name and pay date
        employee_first_name = filtered_employee['employee'].get('first_name', '').replace(' ', '_')
        employee_last_name = filtered_employee['employee'].get('last_name', '').replace(' ', '_')
        month_year = pay_date.strftime('%B_%Y')  # Format: January_2025

        # Create a sanitized filename
        custom_filename = f"Payslip_{employee_first_name}_{employee_last_name}_{month_year}.pdf"
        custom_filename = ''.join(c for c in custom_filename if c.isalnum() or c in ['_', '-', '.'])

        current_app.logger.info(f"Generated custom filename: {custom_filename}")

        # Save PDF to a temporary file with the custom filename
        temp_dir = tempfile.gettempdir()
        temp_file_path = os.path.join(temp_dir, custom_filename)

        with open(temp_file_path, 'wb') as temp_file:
            temp_file.write(pdf_buffer.read())

        # Prepare email content
        subject = f" {employee_first_name} {employee_last_name} Payslip for {pay_date.strftime('%B %Y')}"
        body = f"""
        Dear {filtered_employee['employee'].get('first_name', '')},

        Please find attached your payslip for {pay_date.strftime('%B %Y')}.

        If you have any questions regarding your payslip, please contact the HR department.

        Best regards,
        {company_data.get('company_name', '')} HR Team
        """

        # Send email with attachment
        try:
            Auxillary.send_netpipo_email_attachment(
                subject=subject,
                recipient=employee_email,
                body=body,
                attachment_path=temp_file_path
            )

            # Delete temporary file
            os.unlink(temp_file_path)

            flash(f"Payslip sent to {employee_email} successfully.", 'success')
            current_app.logger.info(f"Payslip sent to {employee_email} successfully.")
            return redirect(url_for('employees.single_employee_payslip', employee_id=employee_id))

        except Exception as e:
            # Delete temporary file in case of error
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

            current_app.logger.error(f"An error occurred while sending email: {e}")
            flash("An error occurred while sending the payslip email.", 'danger')
            return redirect(url_for('employees.single_employee_payslip', employee_id=employee_id))

    except Exception as e:
        current_app.logger.error(f"An error occurred while generating payslip PDF: {e}")
        flash("An error occurred while generating the payslip PDF.", 'danger')
        return redirect(url_for('employees.single_employee_payslip', employee_id=employee_id))


@my_employees_bp.route('/v2/generate_payslips_from_approved_payroll', methods=['GET', 'POST'])
@role_required(['hr', 'accountant', 'manager', 'company_hr'])
def generate_payslips_from_approved_payroll():
    """Generate payslips from approved payroll data.

    This route allows HR to generate payslips for all employees with approved payroll
    for a specific month and year. It can also send the payslips to employees via email.
    """
    # Initialize database connection
    db_connection = DatabaseConnection()
    database_name = session.get('database_name')

    # Get current month and year as default values
    current_date = datetime.now()
    default_month = current_date.month
    default_year = current_date.year

    # Get company data
    company_data = {}
    try:
        company_id = session.get('company_id')
        company = Company.get_company_by_id(company_id)
        if company:
            company_data = company
    except Exception as e:
        current_app.logger.error(f"Error fetching company data: {e}")
        flash("Error fetching company data.", "danger")

    if request.method == 'POST':
        # Get form data
        month = int(request.form.get('month', default_month))
        year = int(request.form.get('year', default_year))
        send_email = request.form.get('send_email') == 'on'

        current_app.logger.info(f"Month: {month}, Year: {year}, Send Email: {send_email}")

        # Validate month and year
        if month < 1 or month > 12:
            flash("Invalid month selected.", "danger")
            return render_template('employees/generate_payslips.html',
                                  default_month=default_month,
                                  default_year=default_year)

        if year < 2000 or year > 2100:
            flash("Invalid year selected.", "danger")
            return render_template('employees/generate_payslips.html',
                                  default_month=default_month,
                                  default_year=default_year)

        # Get approved payroll data for the selected month and year
        try:
            with db_connection.get_session(database_name) as db_session:
                # Get all payrolls for the selected month and year with status 'Approved'
                payrolls = db_session.query(Payroll).filter(
                    Payroll.pay_date >= datetime(year, month, 1),
                    Payroll.pay_date <= datetime(year, month, calendar.monthrange(year, month)[1]),
                    Payroll.status == 'Approved'
                ).all()
                current_app.logger.info(f"Payrolls: {payrolls}")
                if not payrolls:
                    flash(f"No approved payroll data found for {calendar.month_name[month]} {year}.", "warning")
                    return render_template('employees/generate_payslips.html',
                                          default_month=default_month,
                                          default_year=default_year)

                # Process each payroll record
                success_count = 0
                error_count = 0
                email_success_count = 0
                email_error_count = 0

                for payroll in payrolls:
                    try:
                        # Get employee data
                        employee = db_session.query(Employee).filter(Employee.employee_id == payroll.employee_id).first()

                        if not employee:
                            current_app.logger.error(f"Employee not found for payroll ID: {payroll.payroll_id}")
                            error_count += 1
                            continue

                        # Prepare data for PDF generation
                        pay_date = payroll.pay_date
                        first_day = datetime(year, month, 1)
                        last_day = datetime(year, month, calendar.monthrange(year, month)[1])
                        formatted_first_day = first_day.strftime('%d/%m/%Y')
                        formatted_last_day = last_day.strftime('%d/%m/%Y')
                        days_in_month = calendar.monthrange(year, month)[1]

                        # Create employee data dictionary similar to what's used in single_employee_payslip
                        employee_data = {
                            'employee': employee.to_dict(),
                            'basic_needed': payroll.basic_salary,
                            'gross_needed': payroll.gross_salary,
                            'paye': payroll.payee,
                            'pension_ee_value': payroll.employee_pension,
                            'pension_er_value': payroll.employer_pension,
                            'maternity_ee_value': payroll.employee_maternity,
                            'maternity_er_value': payroll.employer_maternity,
                            'rama_ee': payroll.medical_fee,
                            'cbhi_value': payroll.cbhi,
                            'total_deductions_value': payroll.total_deductions,
                            'net_salary_value': payroll.net_salary,
                            'total_reimbursements': payroll.reimbursement or 0,
                            'total_deductions': payroll.other_deductions or 0,
                            'brd_deduction': payroll.brd_deductions or 0,
                            'salary_advance': payroll.advance or 0
                        }
                        current_app.logger.info(f"Employee data: {employee_data}")

                        # Generate PDF
                        prepared_by = session.get('username', 'HR System')
                        pdf_buffer = PDFGenerator.generate_payslip_pdf(
                            employee_data,
                            company_data,
                            pay_date,
                            formatted_first_day,
                            formatted_last_day,
                            days_in_month,
                            prepared_by
                        )

                        # Create a custom filename with employee name and pay date
                        employee_first_name = employee.first_name.replace(' ', '_')
                        employee_last_name = employee.last_name.replace(' ', '_')
                        month_year = pay_date.strftime('%B_%Y')

                        # Create a sanitized filename
                        custom_filename = f"Payslip_{employee_first_name}_{employee_last_name}_{month_year}.pdf"
                        custom_filename = ''.join(c for c in custom_filename if c.isalnum() or c in ['_', '-', '.'])

                        # Save PDF to a temporary file with the custom filename
                        temp_dir = tempfile.gettempdir()
                        temp_file_path = os.path.join(temp_dir, custom_filename)

                        with open(temp_file_path, 'wb') as temp_file:
                            temp_file.write(pdf_buffer.read())

                        success_count += 1

                        # Send email if requested and employee has an email address
                        if send_email:
                            if employee.email:
                                try:
                                    # Prepare email content
                                    subject = f"{employee_first_name} {employee_last_name} Payslip for {pay_date.strftime('%B %Y')}"
                                    body = f"""
                                    <p>Dear {employee.first_name},</p>

                                    <p>Please find attached your payslip for <strong>{pay_date.strftime('%B %Y')}</strong>.</p>

                                    <p>This payslip contains details of your salary, allowances, deductions, and net pay for the period from <strong>{formatted_first_day}</strong> to <strong>{formatted_last_day}</strong>.</p>

                                    <p>If you have any questions regarding your payslip, please contact the HR department.</p>

                                    <p>Best regards,<br>
                                    <strong>{company_data.get('company_name', '')}</strong> HR Team</p>
                                    """

                                    # Send email with attachment
                                    Auxillary.send_netpipo_email_attachment(
                                        subject=subject,
                                        recipient=employee.email,
                                        body=body,
                                        attachment_path=temp_file_path
                                    )

                                    email_success_count += 1
                                    current_app.logger.info(f"Payslip sent to {employee.email} successfully.")
                                except Exception as e:
                                    email_error_count += 1
                                    current_app.logger.error(f"Error sending email to {employee.email}: {e}")

                                # Delete temporary file after sending email
                                if os.path.exists(temp_file_path):
                                    os.unlink(temp_file_path)
                            else:
                                email_error_count += 1
                                current_app.logger.warning(f"Employee {employee.first_name} {employee.last_name} does not have an email address. Skipping email.")
                                flash(f"Employee {employee.first_name} {employee.last_name} does not have an email address. Skipping email.", "warning")
                        else:
                            # Delete temporary file if not sending email
                            if os.path.exists(temp_file_path):
                                os.unlink(temp_file_path)

                    except Exception as e:
                        error_count += 1
                        current_app.logger.error(f"Error generating payslip for employee {payroll.employee_name}: {e}")
                        traceback.print_exc()

                # Show summary message
                if success_count > 0:
                    flash(f"Successfully generated {success_count} payslips.", "success")

                if error_count > 0:
                    flash(f"Failed to generate {error_count} payslips. Check logs for details.", "warning")

                if send_email:
                    if email_success_count > 0:
                        flash(f"Successfully sent {email_success_count} payslips via email.", "success")

                    if email_error_count > 0:
                        flash(f"Failed to send {email_error_count} payslips via email. Check logs for details.", "warning")

                return render_template('employees/generate_payslips.html',
                                      default_month=month,
                                      default_year=year,
                                      success=True)

        except Exception as e:
            current_app.logger.error(f"Error processing payroll data: {e}")
            flash("An error occurred while processing payroll data.", "danger")
            traceback.print_exc()

    try:
        # GET request - show form
        current_app.logger.info("GET request in generate_payslips")
        return render_template('employees/generate_payslips.html',
                            default_month=default_month,
                            default_year=default_year)
    except Exception as e:
        current_app.logger.error(f"Error rendering generate_payslips template: {e}")
        flash("An error occurred while rendering the form.", "danger")
        flash('An error occurred. Please try again later.', 'danger')
        return redirect(url_for('admin_data.dashboard'))


# ATTEMPT TO SOLVE BULK UPDATE

@my_employees_bp.route('/v2/bulk_update_template', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def render_bulk_update_template():
    return render_template('employees/bulk_update_v2.html')


@my_employees_bp.route('/v2/employee_bulk_update', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def employee_bulk_update():
    """Get all employees in the company."""
    from flask_wtf.csrf import generate_csrf
    csrf_token = generate_csrf()

    try:
        # Get the company_id from the session
        company_id = session.get('company_id')
        current_app.logger.info(f"Company ID: {company_id}")
        if not company_id:
            current_app.logger.error("Company ID is missing in session")
            flash('An error occurred. Please try again later.', 'danger')
            return redirect(url_for('admin_data.dashboard'))

        # Get the company database name
        database_name = CompanyHelpers.get_company_database_name(company_id)
        current_app.logger.info(f"Database name: {database_name}")
        if not database_name:
            current_app.logger.error("Database name could not be retrieved")
            flash('An error occurred. Please try again later.', 'danger')
            return redirect(url_for('admin_data.dashboard'))
        # Initialize the database connection and get all employees
        db_connection = DatabaseConnection()
        with db_connection.get_session(database_name) as db_session:
            employees = Employee.get_employees(db_session)
            leave_records = Attendance.get_leave_records(db_session)
            leave_records_map = {}
            for record in leave_records:
                employee_id = str(record["employee_id"])
                if employee_id not in leave_records_map:
                    leave_records_map[employee_id] = []
                leave_records_map[employee_id].append({"time_off_end_date": record["time_off_end_date"],
                                                        "time_off_begin_date":record["time_off_begin_date"]})
            for emp in employees:
                employee_id = str(emp["employee_id"])
                matching_leave_record = leave_records_map.get(employee_id)  # O(1) lookup
                if matching_leave_record:
                    emp["additionals"]=matching_leave_record
                else:
                    continue
            current_app.logger.info(f"Employees: {employees}")
            if not employees:
                current_app.logger.error("No employees found")
                flash('No employees found.', 'danger')
        # Get the attendance_service status from the session
        attendance_service = session.get('attendance_service')
        current_app.logger.info(f"Attendance service status: {attendance_service}")
        return render_template('employees/bulk_update.html', employees=employees,
                           Auxillary=Auxillary, csrf_token=csrf_token,
                           leave_records=leave_records)
    except Exception as e:
        logging.error(f"Error: {e}")
        flash('An error occurred. Please try again later.', 'danger')
        return redirect(url_for('admin_data.dashboard'))

@my_employees_bp.route('/v2/search_employee', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def search_employee():
    from flask_wtf.csrf import generate_csrf
    csrf_token = generate_csrf()
    try:
        param = request.args.get('search-param')
        if not param:
            current_app.logger.error("No search param provided")
            flash('No search parameter provided.', 'danger')
            return redirect(url_for('admin_data.dashboard'))

        company_id = session.get('company_id')
        current_app.logger.info(f"Company ID: {company_id}")
        if not company_id:
            current_app.logger.error("Company ID is missing in session")
            flash('An error occurred. Please try again later.', 'danger')
            return redirect(url_for('admin_data.dashboard'))

        # Get the company database name
        database_name = CompanyHelpers.get_company_database_name(company_id)
        current_app.logger.info(f"Database name: {database_name}")
        if not database_name:
            current_app.logger.error("Database name could not be retrieved")
            flash('An error occurred. Please try again later.', 'danger')
            return redirect(url_for('admin_data.dashboard'))

        db_connection = DatabaseConnection()
        with db_connection.get_session(database_name) as db_session:
            employees = Employee.search_employee(db_session, param)
            leave_records = Attendance.get_leave_records(db_session)
            leave_records_map = {}
            for record in leave_records:
                employee_id = str(record["employee_id"])
                if employee_id not in leave_records_map:
                    leave_records_map[employee_id] = []
                leave_records_map[employee_id].append({"time_off_end_date": record["time_off_end_date"],
                                                        "time_off_begin_date":record["time_off_begin_date"]})
            for emp in employees:
                employee_id = str(emp["employee_id"])
                matching_leave_record = leave_records_map.get(employee_id)  # O(1) lookup
                if matching_leave_record:
                    emp["additionals"]=matching_leave_record
                else:
                    continue
            current_app.logger.info(f"Employees: {employees}")
            return render_template('employees/bulk_update.html', employees=employees,
                               Auxillary=Auxillary, csrf_token=csrf_token
                               )

    except Exception as e:
        logging.error(f"Error: {e}")
        flash('An error occurred. Please try again later.', 'danger')
        return redirect(url_for('admin_data.dashboard'))