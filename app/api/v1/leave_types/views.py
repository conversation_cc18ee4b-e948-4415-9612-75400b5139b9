from flask import Blueprint, request, current_app, jsonify
from app.models.central_leave_types import LeaveTypes
from app.api.v1.decorators.auth_decorators import role_required


leave_types_api_bp = Blueprint('leave_types', __name__)

@leave_types_api_bp.route('/leave_types', methods=['GET'])
@role_required(['admin', 'employee', 'supervisor', 'hr', 'accountant', 'manager', 'company_hr'])
def get_leave_types():
    try:
        leave_types = LeaveTypes.get_leave_types()
        current_app.logger.info('Retrieved leave types successfully')
        return jsonify(success=True, data=leave_types, message='Retrieved leave types successfully'), 200
    except Exception as e:
        current_app.logger.error(f'An error occurred while retrieving leave types: {str(e)}')
        return jsonify(success=False, error='An error occurred while retrieving leave types'), 500


@leave_types_api_bp.route('/leave_type/<uuid:leave_type_id>', methods=['GET'])
@role_required(['admin', 'employee', 'supervisor', 'hr', 'accountant', 'manager', 'company_hr'])
def get_leave_type(leave_type_id):
    try:
        leave_type = LeaveTypes.get_leave_type_by_id(leave_type_id)
        if not leave_type:
            return jsonify(success=False, error='Leave Type not found'), 404
        
        current_app.logger.info('Retrieved leave type successfully')
        return jsonify(success=True, data=leave_type, message='Retrieved leave type successfully'), 200
    except Exception as e:
        current_app.logger.error(f'An error occurred while retrieving leave type: {str(e)}')
        return jsonify(success=False, error='An error occurred while retrieving leave type'), 500


@leave_types_api_bp.route('/add_leave_type', methods=['POST'])
@role_required('admin')
def add_leave_type():
    data = request.get_json()
    name = data.get("name")
    description = data.get("description")

    if not name or not description:
        return jsonify(success=False, error='name and description are required'), 400
    
    try:
        leave_type = LeaveTypes.add_leave_type(name, description)
        current_app.logger.info('Leave Type added successfully', 'success')
        return jsonify(success=True, data=leave_type.to_dict(), message='Leave Type added successfully'), 201
    except Exception as e:
        current_app.logger.error(f'An error occurred while adding Leave Type: {str(e)}')
        return jsonify(success=False, error='An error occurred while adding Leave Type'), 500
        
@leave_types_api_bp.route('/edit_leave_type/<uuid:leave_type_id>', methods=['PUT'])
@role_required(['admin'])
def edit_leave_type(leave_type_id):
    data = request.get_json()
    leave_type = LeaveTypes.get_leave_type_by_id(leave_type_id)
    
    if not leave_type:
        return jsonify(success=False, error='Leave Type not found'), 404
  
    name = data.get("name")
    description = data.get("description")

    if not name or not description:
        return jsonify(success=False, error='Name and description are required'), 400
    
    try:
        updated_leave_type = LeaveTypes.update_leave_type(leave_type_id, name, description)
        current_app.logger.info(f"Leave Type updated successfully: {updated_leave_type}")
        if updated_leave_type:
            return jsonify(success=True, data=updated_leave_type, message='Leave Type updated successfully'), 200
        current_app.logger.error('An error occurred while updating Leave Type', 'danger')
        return jsonify(success=False, error='An error occurred while updating Leave Type'), 500
    except Exception as e:
        current_app.logger.error(f'An error occurred while updating Leave Type: {str(e)}')
        return jsonify(success=False, error='An error occurred while updating Leave Type'), 500
    

@leave_types_api_bp.route('/delete_leave_type/<uuid:leave_type_id>', methods=['DELETE'])
@role_required('admin')
def delete_leave_type(leave_type_id):
    try:
        leave_type = LeaveTypes.get_leave_type_by_id(leave_type_id)
        if not leave_type:
            return jsonify(success=False, error='Leave Type not found'), 404
        
        is_deleted = LeaveTypes.delete_leave_type(leave_type_id)
        current_app.logger.info(f"Leave Type deleted successfully: {is_deleted}")
        if is_deleted:
            return jsonify(success=True, message='Leave Type deleted successfully'), 200
        current_app.logger.error('An error occurred while deleting Leave Type', 'danger')
        return jsonify(success=False, error='An error occurred while deleting Leave Type'), 500
    except Exception as e:
        current_app.logger.error(f'An error occurred while deleting Leave Type: {str(e)}')
        return jsonify(success=False, error='An error occurred while deleting Leave Type'), 500
    
