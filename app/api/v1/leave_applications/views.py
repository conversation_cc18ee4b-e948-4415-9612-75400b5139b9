import json
from flask import Blueprint, jsonify, current_app, request
from flask_jwt_extended import get_jwt, current_user
from app.api.v1.decorators.auth_decorators import role_required
from app.models.company import LeaveApplication, LeaveApproval
from app.models.company import Employee
from app.helpers.auxillary import Auxillary
from app.utils.db_connection import DatabaseConnection
from app.helpers.company_helpers import CompanyHelpers
from app.models.company_approval_work_flow import ApprovalLog
from app.api_helpers.ApiHelpers import UserInputValidator

leave_api_bp = Blueprint("leave_app", __name__)
# Initialize the Database Connection
db_connection = DatabaseConnection()

@leave_api_bp.route('/view_leave_applications', methods=['GET'])
@role_required(['employee', 'manager', 'company_hr', 'accountant', 'hr'])
def view_leave_applications():
    """View leave applications for both company and central users."""
    jwt_data = get_jwt()
    database_name = jwt_data.get("database_name")

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        # Get the employee record
        try:
            current_app.logger.info(f"User info: {current_user}")

            if current_user.get('role') in ['employee', 'supervisor']:
                # can see only his applications
                employee_id = current_user.get('employee_id')
                current_app.logger.info(f"Employee ID: {employee_id}")
                leave_applications = LeaveApplication.get_leave_application_for_employee(db_session, employee_id)
            else:
                # can see all applications associated with this copmany
                leave_applications = LeaveApplication.get_leave_applications(db_session)

            current_app.logger.info(f"Leave Applications: {leave_applications}")
            return jsonify(success=True, data=leave_applications, message='Leave applications retrieved successfully!'), 200
        except Exception as e:
            current_app.logger.error(f"Error getting leave application: {e}")
            return jsonify(success=False, error='An error occurred. Please try again later.'), 500
        

@leave_api_bp.route('/view_leave_approvals', methods=['GET'])
@role_required(['manager', 'company_hr', 'accountant', 'hr', 'supervisor'])
def view_leave_approvals():
    """View all leave approvals for both employees and company staff."""
    jwt_data = get_jwt()
    database_name = jwt_data.get("database_name")

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        try:
            if current_user.get('role') in ['employee', 'supervisor']:
                # can see only his application approvals
                employee_id = current_user.get('employee_id')
                leave_approvals = LeaveApproval.get_leave_approvals_for_employee(db_session, employee_id)
            else:
                # can see all applications approvals associated with this copmany
                leave_approvals = LeaveApproval.get_leave_approvals(db_session)
            return jsonify(success=True, data=leave_approvals, message='Leave approvals retrieved successfully!'), 200
        except Exception as e:
            current_app.logger.error(f"Error getting leave approvals: {e}")
            return jsonify(success=False, error='An error occurred. Please try again later.'), 500


@leave_api_bp.route('/view_approval_logs', methods=['GET'])
@role_required(['manager', 'company_hr', 'accountant', 'hr', 'supervisor'])
def view_approval_logs():
    """View all approval logs."""
    current_app.logger.info('In view_approval_logs route')
    database_name = get_jwt().get('database_name')

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        try:
            approval_logs = ApprovalLog.get_all_logs(db_session)    
            current_app.logger.info(f"approval logs: {approval_logs}")
            return jsonify(success=True, data=approval_logs, message='Approval logs retrieved successfully!'), 200
        except Exception as e:
            current_app.logger.error(f"Error getting approval logs: {e}")
            return jsonify(success=False, error='An error occurred. Please try again later.'), 500
        

@leave_api_bp.route("/apply_for_leave", methods=["POST"])
@role_required("employee")
def apply_for_leave():
    """Apply for a leave or off day."""
    current_app.logger.info('In apply_for_leave route')
    jwt_data = get_jwt()
    user_id = str(current_user.get('user_id'))
    database_name = jwt_data.get('database_name')
    message=""

    #Data from frontend
    data = request.get_json()
    leave_type = data.get('leave_type')
    start_date = data.get('start_date')
    end_date = data.get('end_date')
    reason = data.get('reason')

    current_app.logger.info(f"User ID: {user_id}")
    current_app.logger.info(f"Database Name: {database_name}")
        
    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        try:
            # Get the employee record            
            employee_id = current_user['employee_id']
            if leave_type == "annual_leave":
                days_difference = Auxillary.calculate_days_difference(start_date, end_date)
                is_eligible, message = Employee.can_request_leave_or_off(db_session, employee_id, days_difference)

                if not is_eligible:
                    current_app.logger.info(f"Error applying leave: {message}")
                    return jsonify(success=False, error=message), 400
            
            current_app.logger.info(f"Is User Eligible: {is_eligible}->{message}")
            current_app.logger.info(f"Employee ID: {employee_id}")
            
            # Save the leave application
            result = LeaveApplication.insert_leave_application(db_session, employee_id, 
                                                                leave_type, start_date, 
                                                                end_date, reason)
            current_app.logger.info(f"Leave Application saved: {result}")
            if not result:
                return jsonify(success=False, error='An error occurred. Please try again later.'), 500
            return jsonify(success=True, data=result.to_dict(), message='Leave application submitted successfully'), 200
        except Exception as e:
            current_app.logger.error(f"Error saving leave application: {e}")
            return jsonify(success=False, error='An error occurred. Please try again later.'), 500


@leave_api_bp.route('/approve_leave_application/<uuid:leave_application_id>', methods=['POST'])
@role_required(['manager', 'company_hr', 'accountant', 'hr', 'supervisor'])
def approve_leave_application(leave_application_id):
    """Approve a leave application."""
    current_app.logger.info('In approve_leave_application route')
    data = request.get_json()
    approval = data.get('approval')
    remarks = data.get('remarks')

    # Get JWT data    
    jwt_data = get_jwt()
    identity = json.loads(jwt_data.get('sub'))
    user_id = identity.get('user_id')
    database_name = jwt_data.get('database_name')
    approver_role = identity.get('role')

    is_valid, errors = UserInputValidator.validate({"approval": approval, "remarks": remarks}, 'leave_approval')
    if not is_valid:
        return jsonify(success=False, error=errors), 400
    
    current_app.logger.info(f"approval: {approval}")
    current_app.logger.info(f"remarks: {remarks}")
    current_app.logger.info(f"User ID: {user_id}")
    current_app.logger.info(f"Database Name: {database_name}")

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        try:
            # Get the leave application
            leave_application = LeaveApplication.get_leave_application_by_id(db_session, leave_application_id)
            if not leave_application:
                return jsonify(success=False, error='Leave application not found'), 404
            
            leave_id = leave_application['leave_id']
            leave_type = leave_application['leave_type']
            approver_id = user_id
            current_app.logger.info(f"Leave Application: {leave_application}")
        except Exception as e:
            current_app.logger.error(f"Error getting leave application: {e}")
            return jsonify({'error': 'An error occurred. Please try again later.'}), 500

        try:
            # Save the leave approval
            current_app.logger.info('Saving leave approval')
            result = LeaveApproval.approve_leave_application(db_session, leave_id, approver_id, 
                                                            approver_role, approval, remarks=remarks, leave_type=leave_type)
            current_app.logger.info(f"Leave Approval saved: {result}")
            if len(result) < 0 :
                return jsonify(success=False, error='An error occurred. Please try again later.'), 500
            
            if 'An error occurred' in result:
                return jsonify(success=False, error=f"{result}"), 500
            
            if f'Leave application {approval} successfully by' in result:
                #Update the Approval logs
                log = ApprovalLog.create_log(db_session, leave_application_id, approver_role, approver_id, approval)
                current_app.logger.info(f"Approval Log saved: {log}")
                return jsonify(success=True, message=f"{result}"), 200
        except Exception as e:
            current_app.logger.error(f"Error saving leave approval: {e}")
            return jsonify({'error': 'An error occurred. Please try again later.'}), 500
    return jsonify({'error': 'An error occurred. Please try again later.'}), 500


@leave_api_bp.route('/increment_annual_leave_balance', methods=['POST'])
def increment_annual_leave_balance():
    """
    This endpoint is scheduled to run every 1st day of the month at 12 am.

    Increments the annual leave balance for employees across all companies by 
    retrieving the list of companies, updating employee records.

    Returns:
        JSON response indicating success or an empty dictionary in case of an error.
    """
    
    try:
        companies_db = CompanyHelpers.get_database_names()
        all_employees = CompanyHelpers.get_employees_for_all_companies(companies_db)

        updated_rows = Employee.update_employees_for_all_companies(companies_db, all_employees)
        return jsonify(message="Good", updated_rows=updated_rows)
    except Exception as e:
        current_app.logger.error(f"Error incrementing annual leave balance: {e}")
        return {}
    
@leave_api_bp.route("/increment_extra_leave_days", methods=['POST'])
def increment_extra_leave_days():
    """
    This endpoint is scheduled to run every 12 am every day of the year(based on employee hire date) at 12 am.

    Increments the extra leave days for employees based on their hire date. 
    Employees who have completed a year of service will have their extra_leave_days incremented.
    """
    try:
        companies_db = CompanyHelpers.get_database_names()
        all_employees = CompanyHelpers.get_employees_for_all_companies(companies_db)

        updated_rows = Employee.update_extra_leave_days_for_all_companies(companies_db, all_employees)
        return jsonify(message="Extra leave days incremented successfully", updated_rows=updated_rows)
    except Exception as e:
        current_app.logger.error(f"Error incrementing extra leave days: {e}")
        return {}