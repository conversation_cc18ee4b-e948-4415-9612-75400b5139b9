import os
import json
import requests
from io import BytesIO
from decimal import Decimal
from dotenv import load_dotenv
from datetime import datetime, timedelta
from calendar import monthrange
from concurrent.futures import ThreadPoolExecutor
from flask import Blueprint, request, jsonify, current_app, send_file
from flask_jwt_extended import get_jwt, current_user
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph
from reportlab.lib.styles import getSampleStyleSheet
from app.models.central import Company
from app.models.company import Attendance, Employee, Deductions, NsfContributions, Insurance, User, Site
from app.models.company_documents import Document
from app.utils.db_connection import DatabaseConnection
from app.helpers.company_helpers import CompanyHelpers
from app.routes.payroll.goal_Seek_mine import SalaryCalculatorGross
from app.api.v1.decorators.auth_decorators import role_required
from app.api.jwt_config import redis_client
from app.api_helpers.ApiHelpers import UserInputValidator
from app.helpers.auxillary import Auxillary

attendance_api_bp = Blueprint('attendance', __name__)
BASE_URL = os.getenv('BASE_URL')
executor = ThreadPoolExecutor(max_workers=5)

def make_api_call(url, headers, files):
    """Make a POST request to the Face API."""
    return requests.post(url, headers=headers, files=files)

load_dotenv()


@attendance_api_bp.route('/list_employee_subjects', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def list_employee_subjects():
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    if MICROSERVICE_KEY is None:
        error = "You are not registered to use this service. Please contact <NAME_EMAIL> to be registered."
        return jsonify(success=False, error=error), 401
    
    headers = { 'x-api-key': MICROSERVICE_KEY }
    subjects_url = f"{BASE_URL}/subjects"

    try:
        # Step 1: Get the list of subjects
        subjects_response = requests.get(subjects_url, headers=headers)
        current_app.logger.info(f"Response status: {subjects_response.status_code}")
        current_app.logger.info(f"Response: {subjects_response.text}")

        if subjects_response.status_code not in range(200, 209):
            error = "Error fetching subjects. Please try again."
            return jsonify(success=False, error=error), subjects_response.status_code

        try:
            subjects = subjects_response.json()['subjects']
            current_app.logger.info(f"Subjects: {subjects}")
        except Exception as e:
            current_app.logger.error(f"Error fetching subjects: {e}")
            error = "Error fetching subjects. Please try again."
            return jsonify(success=False, error=error), 500
        
        # Initialize an empty list to store names and employee IDs
        names = []
        employee_ids = []
        employee_names = []
        # Loop through the subjects and get the names
        try:
            for subject in subjects:
                # The name comes after the first 36 characters
                name = subject[36:]
                # take out any trailing spaces
                name = name.strip()
                # Append the name to the list
                names.append(name)
                # The employee ID is the first 36 characters
                employee_id = subject[:36]
                # Remove any trailing spaces
                employee_id = employee_id.strip()
                # Append the employee ID to the list
                employee_ids.append(employee_id)
        except Exception as e:
            current_app.logger.error(f"Error getting names: {e}")
            return jsonify(success=False, error="Failed to fetch employees data"), 500
        
        try:
            database_name = get_jwt().get('database_name')
            db_connection = DatabaseConnection()
            with db_connection.get_session(database_name) as db_session:
                # Get the list of employees from the database
                employees = Employee.get_employees(db_session)
                for employee in employees:
                    current_app.logger.info(f"Employee_ids: {employee_ids}")
                    check = (str(employee['employee_id']) in employee_ids)
                    current_app.logger.info(f"Check: {check}")
                    current_app.logger.info(f"Employee ID: {employee['employee_id']}")
                    current_app.logger.info(f"type of employee ID: {type(employee['employee_id'])} ")
                    current_app.logger.info(f"type of employee_ids: {type(employee_ids)}")
                    for employee_id in employee_ids:
                        current_app.logger.info(f"Employee ID: {employee_id}")
                        current_app.logger.info(f"type of employee ID: {type(employee_id)} ")

                    # We wanna get the list of employees that have been registered in the face collection
                    if str(employee['employee_id']) in employee_ids:
                        continue
                    else:
                        employee_names.append(employee)
        except Exception as e:
            current_app.logger.error(f"Error getting employee names: {e}")
            return jsonify(success=False, error="Failed to fetch employees data"), 500
    except Exception as e:
        current_app.logger.error(f"Error getting subjects: {e}")
        return jsonify(success=False, error="Error getting subjects. Please try again."), 500

@attendance_api_bp.route('/download_image', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def download_image():
    """Download an image from the Face API."""
    # Get the API KEY from CompanyHelpers
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    if MICROSERVICE_KEY is None:
        error = f"""You are not registered to use this service.
        Please contact <NAME_EMAIL> to be registered."""
        return jsonify(success=False, error=error), 401

    headers = { 'x-api-key': MICROSERVICE_KEY }
    base_url = os.getenv('base_url')
    faces_url = f"{BASE_URL}faces"

    try:
        faces_response = requests.get(faces_url, headers=headers)
        current_app.logger.info(f"Response status: {faces_response.status_code}")
        current_app.logger.info(f"Response: {faces_response.text}")
        faces = faces_response.json().get('faces', [])
        current_app.logger.info(f"Faces: {faces}")
        my_faces = []
        for face in faces:
            try:
                image_id = face['image_id']
                current_app.logger.info(f"Image ID: {image_id}")
                image_url = f"{faces_url}/{image_id}/img"

                current_app.logger.info(f"Image URL: {image_url}")
                my_faces.append(image_url)
            except Exception as e:
                current_app.logger.error(f"Error getting image: {e}")
                return jsonify(success=False, error="Error getting image. Please try again."), 500
    except Exception as e:
        current_app.logger.error(f"Error getting faces: {e}")
        return jsonify(success=False, error="Error getting faces. Please try again."), 500

    # check if faces is empty
    if not my_faces:
        return jsonify(success=False, error="No images found"), 400
    
    # Download the first image from the list as an example (you can loop for multiple images)
    for image_url in my_faces:
        try:
            current_app.logger.info(f"Downloading image: {image_url}")
            image_response = requests.get(image_url, headers=headers)
            current_app.logger.info(f"Image response status: {image_response.status_code}")

            if image_response.status_code == 200:
                # Prepare the image to be sent as a file to the user
                image_data = BytesIO(image_response.content)  # Load image into memory
                filename = image_url.split('/')[-2] + ".jpg"  # Use image ID as filename

                # Return the image as a downloadable file
                return send_file(image_data, mimetype='image/jpeg', as_attachment=True, download_name=filename)
            else:
                current_app.logger.error(f"Failed to download image, status: {image_response.status_code}")
                return jsonify(success=False, error="Failed to get image"), 500
        except Exception as e:
            current_app.logger.error(f"Error downloading image: {e}")
            return jsonify(success=False, error="Error downloading image"), 500
    
    return jsonify(success=False, error="No images could be downloaded"), 500

@attendance_api_bp.route('/proxy_image', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def proxy_image():
    """Fetch and serve an image from the Face API using the full URL with the required headers."""
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    image_url = request.args.get('image_url')  # Retrieve the image URL from the query parameter

    if not MICROSERVICE_KEY or not image_url:
        return jsonify(success=False, error="Missing API key or image URL."), 400

    headers = {'x-api-key': MICROSERVICE_KEY}

    try:
        response = requests.get(image_url, headers=headers)
        if response.status_code == 200:
            # Serve the image as a file response
            image_data = BytesIO(response.content)
            return send_file(image_data, mimetype='image/jpeg')
        else:
            return jsonify("Failed to fetch the image."), response.status_code
    except Exception as e:
        current_app.logger.error(f"Error fetching image: {e}")
        return jsonify(success=False, error="Failed to fetch the image."), 500

@attendance_api_bp.route('/attendance_records', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def attendance_records():
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    # Check if API KEY is None and redirect to dashboard
    if MICROSERVICE_KEY is None:
        error = f"""
        You are not registered to use this service.
        Please contact <NAME_EMAIL> to be registered.
        """
        return jsonify(success=False, error=error), 401
    
    # Replace SSR POST request for filtering to GET request with query parameters
    if request.args:
        start_period = request.args.get('start_period', None)
        end_period = request.args.get('end_period', None)

        try:
            attendance = Attendance.get_all_attendance_in_range(db_session, start_period, end_period)
            locations = Attendance.get_unique_locations(attendance)
            current_app.logger.info(f"Locations: {locations}")
            current_app.logger.info(f"Start period: {start_period}, End period: {end_period}")
            current_app.logger.info(f"Attendance number: {len(attendance)}")
            
            attendance_data = {'attendance': attendance, 'locations': locations}
            return jsonify(success=True, data=attendance_data, message="Attendance records retrieved successfully"), 200
        except Exception as e:
            current_app.logger.error(f"Error getting attendance: {e}")
            return jsonify(success=False, error="Failed to load attendance data"), 500

    # Get the database name from the session
    database_name = get_jwt().get('database_name')
    db_connection = DatabaseConnection()
    with db_connection.get_session(database_name) as db_session:
        try:
            attendance = Attendance.get_attendance(db_session)
            current_app.logger.info(f"Attendance records retrieved: {attendance}")
            # we wanna group the attendance by clocked_in, field_clockin
            clocked_in = [record for record in attendance if record['time_in'] is not None]
            field_clockin = [record for record in attendance if record['field_in'] is None]
            
            attendance_data = {'clocked_in': clocked_in, 'field_clockin': field_clockin}
            return jsonify(success=True, data=attendance_data, message="Attendance records retrieved successfully"), 200
        except Exception as e:
            current_app.logger.error(f"Error getting attendance: {e}")
            return jsonify(success=False, error="Failed to load attendance data"), 500

@attendance_api_bp.route('/timesheet', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def timesheet():
    """Get the timesheet of all employees from the database."""
    jwt_data = get_jwt()
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    if MICROSERVICE_KEY is None:
        error = """
        You are not registered to use this service.
        Please contact <NAME_EMAIL> to be registered.
        """
        current_app.logger.error(f"Error: {error}")
        return jsonify(success=False, error=error), 400

    database_name = jwt_data.get('database_name')
    company_id = jwt_data.get('company_id')

    if not database_name or not company_id:
        current_app.logger.error("Database name or company name could not be retrieved")
        return jsonify(success=False, error='Try again or re-login'), 400

    # Determine the current month and year based on form input or current date
    if request.args:
        period = request.args.get('period', None)
    else:
        period = datetime.now()
    current_month = period.month
    current_year = period.year
    current_month_name = period.strftime('%B')
    total_days_in_month = monthrange(current_year, current_month)[1]
    current_app.logger.info(f"total days in month: {total_days_in_month} and the type is {type(total_days_in_month)}")

    db_connection = DatabaseConnection()
    with db_connection.get_session(database_name) as db_session:
        try:
            employees = Employee.get_employees(db_session)
            current_app.logger.info("Employees retrieved successfully")
        except Exception as e:
            current_app.logger.error(f"Error retrieving employees: {e}")
            return jsonify(sucess=False, error="Failed to load employee data"), 500

        employees_attendance = []

        for employee in employees:
            allowances = employee['allowances']
            transport_allowance = employee['transport_allowance']
            emp_deductions = Deductions.get_deduction_for_given_month_and_year_for_employee(db_session, employee['employee_id'],current_month, current_year)
            total_deductions = sum(d['deduction_amount'] for d in emp_deductions)

            # RSSB Contributions fetching logic
            rssb_contributions = NsfContributions.query.all()
            if not rssb_contributions:
                error = "No RSSB contributions found."
                current_app.logger.error(f"Error: {error}")
                return jsonify(success=False, error=error), 404
            
            for contribution in rssb_contributions:
                contribution_name = contribution.contribution_name
                if contribution_name == "maternity":
                    maternity_er_rate = contribution.employer_rate
                    maternity_ee_rate = contribution.employee_rate
                elif contribution_name == "pension":
                    pension_er_rate = contribution.employer_rate
                    pension_ee_rate = contribution.employee_rate
                elif contribution_name == "cbhi":
                    cbhi_ee_rate = contribution.employee_rate
                # This else is added during api development - idea: skip anything if it does not meet the criteria
                else:
                    continue
            
            try:
                insurance = Insurance.get_insurances(db_session)
                if not insurance or len(insurance) == 0:
                    rama_ee_rate = 0
                else:
                    # Check if the insurance name is rama
                    if insurance[0]["insurance_name"] == "rama":
                        rama_ee_rate = insurance[0]["employee_rate"]
                    else:
                        rama_ee_rate = 0
            except Exception as e:
                current_app.logger.error(f"Error fetching insurance: {e}")
                rama_ee_rate = Decimal('0.00')

            try:
                # Fetch employee attendance
                attendance = Attendance.get_attendance_for_employee(db_session, employee['employee_id'])
                current_app.logger.info(f"Attendance records retrieved for employee with name: {employee['first_name']} {employee['last_name']} are: {attendance}")
                if not attendance:
                    current_app.logger.warning(f"No attendance records found for employee {employee['employee_id']}")
                    attendance = []
                days_present, days_on_leave, days_off, total_hours_worked = calculate_attendance(
                    attendance, current_year, current_month, total_days_in_month
                )
                current_app.logger.info(f"Days present: {days_present}, Days on leave: {days_on_leave}, Days off: {days_off}, Total hours worked: {total_hours_worked}")
            except Exception as e:
                current_app.logger.error(f"Error processing attendance for employee {employee['employee_id']}: {e}")
                days_present, days_on_leave, days_off, total_hours_worked = set(), set(), set(), timedelta()

            # Calculate attendance details
            try:
                attendance_status = get_attendance_status(days_present, days_on_leave, days_off, total_days_in_month)
            except Exception as e:
                current_app.logger.error(f"Error calculating attendance status: {e}")
                attendance_status = ['A'] * total_days_in_month
            try:
                days_worked = len(days_present)
            except Exception as e:
                current_app.logger.error(f"Error calculating days worked: {e}")
                days_worked = 0
            try:
                days_leave = len(days_on_leave)
            except Exception as e:
                current_app.logger.error(f"Error calculating days on leave: {e}")
                days_leave = 0
            try:
                days_off_count = len(days_off)
            except Exception as e:
                current_app.logger.error(f"Error calculating days off: {e}")
                days_off_count = 0
            try:
                days_absent = total_days_in_month - (days_worked + days_leave + days_off_count)
            except Exception as e:
                current_app.logger.error(f"Error calculating days absent: {e}")
                days_absent = 0
            try:
                total_hours_worked = round(total_hours_worked.total_seconds() / 3600, 2)
            except Exception as e:
                current_app.logger.error(f"Error calculating total hours worked: {e}")
                total_hours_worked = 0
            try:
                paid_days = days_worked + days_leave + days_off_count
            except Exception as e:
                current_app.logger.error(f"Error calculating paid days: {e}")
                paid_days = 0

            try:
                if employee['net_salary']:
                    applicable_net_salary = round((employee['net_salary'] / Decimal(total_days_in_month)) * Decimal(paid_days))
                    current_app.logger.info(f"Applicable net salary: {applicable_net_salary}")
                elif employee['gross_salary']:
                    # Calculate net salary from gross salary
                    calculation = SalaryCalculatorGross(allowances, transport_allowance, total_deductions,
                                                        cbhi_ee_rate, pension_ee_rate, pension_er_rate,
                                                        maternity_ee_rate, maternity_er_rate, rama_ee_rate,
                                                        employee['employee_type'])
                    basic_salary = calculation.calculate_basic_salary(employee['gross_salary'])
                    results = calculation.calculate_all(basic_salary)
                    
                    # Unpack calculated results
                    (gross_needed, rama_ee, cbhi_value, paye, net_bcbhi, net_cbhi, pension_ee_value, pension_er_value,
                        maternity_ee_value, maternity_er_value, total_deductions_value, net_salary_value) = map(round, results)
                    
                    applicable_net_salary = round((net_salary_value / total_days_in_month) * paid_days)
                    current_app.logger.info(f"Applicable net salary: {applicable_net_salary}")
                    current_app.logger.info(f"Results: {results}")
                    current_app.logger.info(f"Net salary: {net_salary_value}")
                elif employee['total_staff_cost']:
                    gross_salary = Employee.calculate_gross_based_on_total_staff_cost(db_session, employee, calculation_date=datetime.now().date())
                    calculate = SalaryCalculatorGross(allowances, transport_allowance, total_deductions,
                                                        cbhi_ee_rate, pension_ee_rate, pension_er_rate,
                                                        maternity_ee_rate, maternity_er_rate, rama_ee_rate,
                                                        employee['employee_type'])
                    basic_salary = calculate.calculate_basic_salary(gross_salary)
                    result = calculate.calculate_all(basic_salary)
                    # Unpack calculated results
                    (gross_needed, rama_ee, cbhi_value, paye, net_bcbhi, net_cbhi, pension_ee_value, pension_er_value,
                    maternity_ee_value, maternity_er_value, total_deductions_value, net_salary_value) = map(round, result)
                    applicable_net_salary = round((net_salary_value / Decimal(total_days_in_month)) * Decimal(paid_days))
            except Exception as e:
                current_app.logger.error(f"Error calculating applicable net salary: {e}")
                applicable_net_salary = Decimal('0.00')
            
            employees_attendance.append({
                'employee': employee,
                'attendance_status': attendance_status,
                'days_worked': days_worked,
                'days_absent': days_absent,
                'attendance': attendance,
                "days_leave": days_leave,
                'days_off_count': days_off_count,
                'total_hours_worked': total_hours_worked,
                'paid_days': paid_days,
                'applicable_net_salary': applicable_net_salary
            })
        
        # Get company name
        company_name = CompanyHelpers.get_company_name(company_id)
        timesheet_data = dict(
            employees_attendance=employees_attendance,
            total_days_in_month=total_days_in_month,
            company_name=company_name,
            current_month_name=current_month_name,
            current_year=current_year
        )
        # Store timesheet data in redis for later use - this replaces SSR session based mechanism
        # Store attendance details in session
        redis_client.set(jwt_data.get('user_id'), json.dumps(timesheet_data), ex=current_app.config["JWT_ACCESS_TOKEN_EXPIRES"])
        return jsonify(success=True, data=timesheet_data, message="Timesheet data retrieved successfully."), 200

def calculate_attendance(attendance, current_year, current_month, total_days_in_month):
    """Calculate attendance details for an employee."""
    days_present = set()
    days_on_leave = set()
    days_off = set()
    total_hours_worked = timedelta()

    if not isinstance(attendance, list):
        current_app.logger.error(f"Invalid attendance data format: {attendance}")
        return days_present, days_on_leave, days_off, total_hours_worked

    for record in attendance:
        try:
            if 'time_in' not in record or 'work_status' not in record:
                current_app.logger.warning(f"Malformed record encountered: {record}")
                continue

            if record['time_in'] is None:
                time_off_days = calculate_time_off_days(record, current_year, current_month, total_days_in_month)
                current_app.logger.info("Time off days: ", time_off_days)
                if record['work_status'].lower() == 'leave':
                    days_on_leave.update(time_off_days)
                elif record['work_status'].lower() == 'off':
                    days_off.update(time_off_days)
            else:
                record['time_in'] = datetime.strptime(record['time_in'], '%d/%m/%Y %H:%M:%S')
                if record['time_in'].year == current_year and record['time_in'].month == current_month:
                    days_present.add(record['time_in'].day)
                    if record.get('total_duration'):
                        total_hours_worked += record['total_duration']
        except IndexError as ie:
            current_app.logger.error(f"Index error in record processing: {ie}")
            raise
        except Exception as e:
            current_app.logger.error(f"Error processing record: {e}")
            raise

    return days_present, days_on_leave, days_off, total_hours_worked

def calculate_time_off_days(record, current_year, current_month, total_days_in_month):
    """Calculate the days an employee was on leave or off."""
    try:
        time_off_begin_date = datetime.strptime(record['time_off_begin_date'], '%d/%m/%Y')
        time_off_end_date = datetime.strptime(record['time_off_end_date'], '%d/%m/%Y')

        current_app.logger.info(f"Time off begin date: {time_off_begin_date}")
        current_app.logger.info(f"Time off end date: {time_off_end_date}")
        current_app.logger.info(f"Current year: {current_year}")
        current_app.logger.info(f"Current month: {current_month}")
        current_app.logger.info(f"Total days in month: {total_days_in_month}")
        current_app.logger.info(f"Attendance records: {record}")

        # Ensure the time-off period overlaps with the current month
        if (time_off_begin_date.year != current_year or
            time_off_end_date.year != current_year or
            time_off_begin_date.month != current_month and
            time_off_end_date.month != current_month):
            current_app.logger.info("Time-off dates do not fall within the current month.")
            return set()

        # Adjust dates to the current month range
        time_off_begin_date = max(time_off_begin_date, datetime(current_year, current_month, 1))
        time_off_end_date = min(time_off_end_date, datetime(current_year, current_month, total_days_in_month))

        current_app.logger.info(f"Adjusted time off begin date: {time_off_begin_date}")
        current_app.logger.info(f"Adjusted time off end date: {time_off_end_date}")

        # Calculate days
        return {
            (time_off_begin_date + timedelta(days=i)).day
            for i in range((time_off_end_date - time_off_begin_date).days + 1)
        }
    except Exception as e:
        current_app.logger.error(f"Error calculating time off days: {e}")
        return set()

def get_attendance_status(days_present, days_on_leave, days_off, total_days_in_month):
    """Generate attendance status for the days of the month."""
    return [
        'P' if day in days_present else
        'L' if day in days_on_leave else
        'O' if day in days_off else
        'A' for day in range(1, total_days_in_month + 1)
    ]

from reportlab.lib.pagesizes import A4, landscape

@attendance_api_bp.route('/export_timesheet_excel', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def export_timesheet_excel():
    """Export the timesheet data to Excel format."""
    current_app.logger.info("=== Starting timesheet Excel export ===")

    jwt_data = get_jwt()
    redis_timesheet_data = json.loads(redis_client.get(jwt_data.get('user_id')))
    database_name = jwt_data.get('database_name')
    if not database_name:
        current_app.logger.error("Database name not found in JWT data.")
        return jsonify(success=False, message="Try again later or re-login."), 400
    
    employees_attendance = redis_timesheet_data.get('employees_attendance')
    total_days_in_month = redis_timesheet_data.get('total_days_in_month')
    company_name = redis_timesheet_data.get('company_name')
    current_month_name = redis_timesheet_data.get('current_month_name')
    current_year = redis_timesheet_data.get('current_year')

    print(f"Session data: employees={len(employees_attendance) if employees_attendance else 0}, "
          f"days={total_days_in_month}, company={company_name}, month={current_month_name}")

    if not employees_attendance:
        current_app.logger.info("No timesheet data available in session")
        return jsonify(success=False, error="No timesheet data available. Please generate a timesheet first."), 400

    try:
        # Import necessary libraries
        import pandas as pd
        from io import BytesIO

        current_app.logger.info("Creating simple Excel file")
        
        # Create a simple DataFrame with just the essential data
        data = []
        for idx, employee in enumerate(employees_attendance, 1):
            emp_name = f"{employee['employee']['first_name']} {employee['employee']['last_name']}"

            # Basic row with employee info
            row = {
                'No': idx,
                'Employee Name': emp_name,
                'Days Worked': employee['days_worked'],
                'Hours Worked': employee['total_hours_worked'],
                'Days Off': employee['days_off_count'],
                'Absent': employee['days_absent'],
                'Leave': employee['days_leave'],
                'Days Payable': employee['paid_days'],
                'Net': employee['applicable_net_salary']
            }

            # Add attendance status for each day
            for day, status in enumerate(employee['attendance_status'], 1):
                row[f'Day {day}'] = status

            data.append(row)
        
        # Create DataFrame
        current_app.logger.info("Creating DataFrame")
        df = pd.DataFrame(data)

        # Create Excel file
        current_app.logger.info("Writing to Excel")
        output = BytesIO()
        df.to_excel(output, index=False)
        output.seek(0)

        # Generate filename
        filename = f"{company_name.replace(' ', '_') if company_name else 'Company'}_Timesheet_{current_month_name}_{current_year}.xlsx"
        current_app.logger.info(f"Filename: {filename}")

        # Send file
        current_app.logger.info("Sending file")
        return send_file(
            output,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    except Exception as e:
        current_app.logger.error(f"Error generating Excel file: {e}")
        return jsonify(success=False, error="Failed to export to excel. Try again later."), 500

@attendance_api_bp.route('/v2/download_timesheet', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def download_timesheet():
    """Download the timesheet Excel file for the current month."""
    jwt_data = get_jwt()
    redis_attendance_data = json.loads(redis_client.get(jwt_data.get('user_id')))
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    if MICROSERVICE_KEY is None:
        error = "You are not registered to use this service. Please contact <NAME_EMAIL> to be registered."
        return jsonify(success=False, error=error), 400
    
    # Retrieve redis attendance data data
    database_name = jwt_data.get('database_name')
    if not database_name:
        return jsonify(success=False, error="Try again later or re-login."), 400

    company_name = CompanyHelpers.get_company_name(jwt_data.get('company_id'))
    if not company_name:
        return jsonify(success=False, error="Unexpected error occurred. Try again later."), 500
    
    try:
        employees_attendance = redis_attendance_data.get('employees_attendance')
        current_month_name = redis_attendance_data.get('current_month_name')
        current_year = redis_attendance_data.get('current_year')
        total_days_in_month = redis_attendance_data.get('total_days_in_month')
    except Exception as e:
        current_app.logger.error(f"Error getting session information: {e}")
        return jsonify(success=False, error="Failed to to process timesheet data"), 500
    
    try:
        output = BytesIO()
        doc = SimpleDocTemplate(output, pagesize=landscape(A4))
        styles = getSampleStyleSheet()

        elements = [
            Paragraph(f"<strong>{company_name} Timesheet</strong>", styles['Title']),
            Paragraph(f"<strong>{current_month_name} {current_year}</strong>", styles['Title']),
            Paragraph("Company TIN: *********", styles['Normal']),
        ]

        headers = ['NO', 'NAME', 'BRANCH'] + [str(day) for day in range(1, total_days_in_month + 1)] + ['Days Worked', 'Hours', 'Off', 'Absent(A)', 'Leave (L)', 'Days Payable', 'Net']
        table_data = [headers]

        for employee in employees_attendance:
            db_connection = DatabaseConnection()
            with db_connection.get_session(database_name) as db_session:
                try:
                    site_id = employee['employee']['site_id']
                    site = Site.get_site_by_id(db_session, site_id)
                    site_name = site['site_name']
                except Exception as e:
                    current_app.logger.error(f"Error getting site name: {e}")
                    site_name = 'N/A'
            row = [
                len(table_data),
                f"{employee['employee']['first_name']} {employee['employee']['last_name']}",
                site_name,
            ] + employee['attendance_status'] + [
                employee['days_worked'],
                employee['total_hours_worked'],
                employee['days_off_count'],
                employee['days_absent'],
                employee['days_leave'],
                employee['paid_days'],
                employee['applicable_net_salary'],
            ]
            table_data.append(row)

        from reportlab.lib.units import inch

        # Calculate the column widths for landscape layout
        page_width = landscape(A4)[0] - doc.leftMargin - doc.rightMargin  # Update for landscape
        num_columns = len(headers)

        # Define specific column widths
        # Adjust these widths based on the content and layout you want
        col_widths = [
            0.5 * inch,  # Width for 'NO'
            2.0 * inch,  # Width for 'EMPLOYEE NAME'
            1.2 * inch,  # Width for 'LOCATION'
        ] + [0.5 * inch] * (total_days_in_month) + [0.5 * inch] * 7  # Rest of the columns with smaller widths

        # Ensure the total widths do not exceed the available page width
        if sum(col_widths) > page_width:
            # You may need to scale down some widths if they exceed page width
            scale_factor = page_width / sum(col_widths)
            col_widths = [width * scale_factor for width in col_widths]

        # Create the table with the defined column widths
        table = Table(table_data, colWidths=col_widths)

        # Apply styles to the table as before
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),  # Adjust font size here if needed
            ('BOTTOMPADDING', (0, 0), (-1, 0), 6),
            ('TOPPADDING', (0, 1), (-1, -1), 3),
        ]))

        elements.append(table)

        doc.build(elements)
        output.seek(0)

        return send_file(output, as_attachment=True, download_name='Employee_Timesheet.pdf', mimetype='application/pdf')
    except Exception as e:
        current_app.logger.error(f"Error creating or saving PDF: {e}")
        return jsonify(success=False, error="Failed to create or save PDF"), 500

@attendance_api_bp.route('/list_daily_attendance', methods=['GET'])
@role_required(['hr', 'accountant', 'manager', 'company_hr', 'supervisor'])
def list_daily_attendance():
    """List the daily attendance records for all employees."""
    # Get JWT data
    jwt_data = get_jwt()

    # Get the API KEY from CompanyHelpers
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    if MICROSERVICE_KEY is None:
        error = """
        You are not registered to use this service.
        Please contact <NAME_EMAIL> to be registered.
        """
        return jsonify(success=False, error=error), 400

    # Get the database name from the session
    database_name = jwt_data.get('database_name')
    db_connection = DatabaseConnection()
    with db_connection.get_session(database_name) as db_session:
        try:
            daily_attendance = Attendance.get_attendance_by_date(db_session, datetime.now().date())
            current_app.logger.info(f"Daily attendance records retrieved: {daily_attendance}")

            # get the daily attendance records of clockin
            clockin_attendance = daily_attendance['clocked_in']
            current_app.logger.info(f"Clockin attendance records retrieved: {clockin_attendance}")

            # Get the daily field attendance records
            field_attendance = daily_attendance['field_clockin']
            current_app.logger.info(f"Field attendance records retrieved: {field_attendance}")

            daily_attendance_data = {"clockin_attendance": clockin_attendance, "field_attendance": field_attendance}
            return jsonify(success=True, data=daily_attendance_data, message="Daily attendance records retrieved successfully"), 200
        except Exception as e:
            current_app.logger.error(f"Error getting daily attendance records: {e}")
            return jsonify(success=False, error="Failed to fetch daily attendance records"), 500

@attendance_api_bp.route('/get_previous_day_attendance', methods=['GET'])
@role_required(['hr', 'accountant', 'manager', 'company_hr', 'supervisor'])
def get_previous_day_attendance():
    """Get the attendance records for the previous day."""
    jwt_data = get_jwt()
    # Get the API KEY from CompanyHelpers
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    if MICROSERVICE_KEY is None:
        error = """
        You are not registered to use this service.
        Please contact <NAME_EMAIL> to be registered.
        """
        return jsonify(success=False, error=error), 400

    # Get the database name from JWT
    database_name = jwt_data.get('database_name')
    db_connection = DatabaseConnection()
    
    # Connect to the database and fetch the attendance records for the previous day
    with db_connection.get_session(database_name) as db_session:
        try:
            # Get the previous day's date
            previous_day = datetime.now() - timedelta(days=1)
            previous_day_attendance = Attendance.get_attendance_by_date(db_session, previous_day.date())

            # send an email to the supervisor of a company
            company_users = User.get_users(db_session)
            users_emails = {supervisor['email'] for supervisor in company_users if supervisor['role'] == 'supervisor'}
            
            # Get company users from the central database
            company_id = '6f8faf26-b2fa-48c4-8dd7-5e02f8cf0946'
            central_users = Company.get_users_for_company(company_id)
            current_app.logger.info(f"Central users before converting to a dictionary: {central_users}")
            central_users = [user.to_dict() for user in central_users]
            central_user_emails = {user['email'] for user in central_users}
            
            # Add central_user_emais to user_emails - I used set to avoid duplicates
            users_emails.update(central_user_emails)
            current_app.logger.info(f"Users emails: {users_emails}")

            # Send the email to the users now that we have emails
            subject = f"Attendance records for {previous_day.strftime('%d/%m/%Y')}"
            for record in previous_day_attendance.get('clocked_in', []):
                if record.get('time_out') is None:
                    time_out = 'Not yet clocked out'
                else:
                    time_out = record.get('time_out')
            # arrange the body to include the attendance records details as a table
            # Generate the email body with an HTML table
            body = f"""
                <html>
                    <head>
                    <style>
                        body {{
                            font-family: Poppins, sans-serif;
                            background-color: #f4f4f4;
                            color: #333;
                            padding: 2rem;
                        }}
                        h2 {{
                            color: #007BFF;
                        }}
                        table {{
                            width: 100%;
                            border-collapse: collapse;
                            margin-top: 20px;
                        }}
                        th, td {{
                            border: 1px solid #ddd;
                            padding: 10px;
                            text-align: left;
                        }}
                        th {{
                            background-color: #ebfaf0;
                            color:#25a38b;
                        }}
                        .footer {{
                            margin-top: 20px;
                            font-size: 14px;
                            color: #25a38b;
                        }}
                    </style>
                </head>
                <body>
                    <p>
                        Hello, Please find the attendance records for {previous_day.strftime('%d/%m/%Y')} below.
                    </p>
                    <h3>Clocked In</h3>
                    <table>
                        <tr>
                            <th>Employee Name</th>
                            <th>Time In</th>
                            <th>Time Out</th>
                            <th>Duration</th>
                            <th>Clock-in Location</th>
                            <th>Device Used</th>
                        </tr>
                        {''.join(f'<tr><td>{record["employee_name"]}</td><td>{record["time_in"]}</td><td>{time_out}</td><td>{record["total_duration"]}</td><td>{record["clockin_location"]}</td><td>{record["device_used"]}</td></tr>' for record in previous_day_attendance.get("clocked_in", []))}
                    </table>
                    <h3>On Leave</h3>
                    <table>
                        <tr>
                            <th>Employee Name</th>
                            <th>Leave Start</th>
                            <th>Leave End</th>
                        </tr>
                        {''.join(f'<tr><td>{record["employee_name"]}</td><td>{record["time_off_begin_date"]}</td><td>{record["time_off_end_date"]}</td></tr>' for record in previous_day_attendance.get("on_leave", []))}
                    </table>
                    <h3>On Off</h3>
                    <table>
                        <tr>
                            <th>Employee Name</th>
                            <th>Off Start</th>
                            <th>Off End</th>
                        </tr>
                        {''.join(f'<tr><td>{record["employee_name"]}</td><td>{record["time_off_begin_date"]}</td><td>{record["time_off_end_date"]}</td></tr>' for record in previous_day_attendance.get("on_off", []))}
                    </table>
                </body>
            </html>
            """

            try:
                # send email to all reciepients
                sent = Auxillary.send_netpipo_email( subject, users_emails, body)
                current_app.logger.info(f"Email sent: {sent}")
            except Exception as e:
                current_app.logger.error(f"Error sending email: {e}")
                return jsonify(success=False, error="Failed to send email."), 500
            
            # Convert timedelta fields to string
            for record in previous_day_attendance.get('clocked_in', []):
                if isinstance(record.get('total_duration'), timedelta):
                    record['total_duration'] = str(record['total_duration'])

            current_app.logger.info(f"Previous day attendance records retrieved: {previous_day_attendance}")
            return jsonify(success=True, data=previous_day_attendance, message="Previous day attendance records retrieved successfully"), 200
        except Exception as e:
            current_app.logger.error(f"Error getting previous day attendance records: {e}")
            return jsonify(success=False, error="Failed to get previous day attendance records"), 500

@attendance_api_bp.route('/view_voided_attendance', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def view_voided_attendance():
    """View voided attendance records for all employees."""
    # Get JWT data
    jwt_data = get_jwt()

    # Get the API KEY from CompanyHelpers
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    if MICROSERVICE_KEY is None:
        error = """
        You are not registered to use this service.
        Please contact <NAME_EMAIL> to be registered.
        """
        return jsonify(success=False, error=error), 4000

    # Get the database name from the session
    database_name = jwt_data.get('database_name')
    db_connection = DatabaseConnection()

    # Connect to the database and get the voided attendance records
    with db_connection.get_session(database_name) as db_session:
        try:
            voided_attendance = Attendance.get_voided_attendance(db_session)
            current_app.logger.info("Voided attendance records retrieved")

            return jsonify(success=True, data=voided_attendance, message="Voided attendance records retrieved successfully"), 200
        except Exception as e:
            current_app.logger.error(f"Error getting voided attendance records: {e}")
            return jsonify(success=False, error="Failed to fetch voided attendance records"), 500

@attendance_api_bp.route('/void_attendance_record/<attendance_id>', methods=['POST'])
@role_required(['hr', 'accountant', 'manager', 'company_hr', 'supervisor'])
def void_attendance_record(attendance_id):
    """Void an attendance record for an employee."""

    jwt_data = get_jwt()
    data = request.get_json()

    # Get the API KEY from CompanyHelpers
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    if MICROSERVICE_KEY is None:
        error = """
        You are not registered to use this service.
        Please contact <NAME_EMAIL> to be registered.
        """
        return jsonify(success=False, error=error), 401

    # Extract data from the form
    status = data.get('status')
    reason = data.get('reason')

    if not status or not reason:
        return jsonify(success=False, error="Status and reason are required"), 400
    
    # Get the database name from the session
    database_name = jwt_data.get('database_name')
    db_connection = DatabaseConnection()

    with db_connection.get_session(database_name) as db_session:
        try:
            result = Attendance.change_attendace_status(db_session, attendance_id, status, reason)
            current_app.logger.info(f"Attendance record voided: {result}")
            if status == "void":
                message = "Attendance record voided successfully."
            elif status == "unvoid":
                message = "Attendance record restored."
            else:
                message = "Attendance record status updated."

            current_app.logger.info(f"Attendance record voided: {result}")
            if result is False:
                return jsonify(success=False, error="Failed to void attendance record"), 500
            
            return jsonify(success=True, message=message), 200
        except Exception as e:
            current_app.logger.error(f"Error voiding attendance record: {e}")
            return jsonify(success=False, error="Failed to update record status")

@attendance_api_bp.route('/clockin', methods=['POST'])
@role_required('employee')
def clockin():
    """API endpoint for clock in an employee using facial recognition."""
    jwt_data = get_jwt()
    try:
        # JWT data
        company_id = jwt_data.get('company_id')
        database_name = jwt_data.get('database_name')
        employee_id = current_user.get("employee_id")
        # user_id = str(current_user.get("user_id"))
    except Exception as e:
        current_app.logger.error(f"Failed to load JWT data: {e}")
        return jsonify(success=False, error="An error occurred. Try again or re-login"), 400
    
    # Get the API KEY
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    if MICROSERVICE_KEY is None:
        error="You are not registered to use this service. Please contact <NAME_EMAIL> to be registered."
        current_app.logger.error(f"API Clockin - Error: {error}")
        return jsonify(succes=False, error=error), 401

    current_app.logger.info(f"API Clockin - Using session data: Company ID: {company_id}, Database: {database_name}, Employee ID: {employee_id}")
    current_app.logger.info(f"API Clockin - MICROSERVICE_KEY: {MICROSERVICE_KEY}")

    try:
        # Process the uploaded image
        if ('image' not in request.files) or (request.files['image'] and request.files['image'].filename == ''):
            current_app.logger.error("API Clockin - No image part in the request")
            return jsonify(success=False, error="No image selected"), 400

        # Grab the image from request
        image_file = request.files['image']

        # Get location data
        latitude = request.form.get('latitude')
        longitude = request.form.get('longitude')
        location = f"{latitude}, {longitude}"
        current_app.logger.info(f"API Clockin - Location: {location}")

        # Get device info
        device_used = request.form.get('device_used')
        if device_used is None:
            device_used = "Web Browser"
            current_app.logger.warning(f"API Clockin - device_used was None, setting default value: {device_used}")
    except Exception as e:
        current_app.logger.error(f"Failed to process request data: {e}")
        return jsonify(success=False, error="Check request body"), 400
    
    # Connect to the database
    db_connection = DatabaseConnection()
    with db_connection.get_session(database_name) as db_session:
        # Protect user to send request when he/she already clockedin
        user = Employee.get_employee_by_email(db_session, current_app['email'])
        user_attendance = user.attendance
        if user_attendance.time_out == None and user_attendance.work_status == None and user_attendance.is_void == False:
            current_app.logger.error(f"API Clockin - Error: You have already clocked in")
            return jsonify(success=False, error="You have already clocked in"), 400
        
        # Verify the employee's face
        BASE_URL = os.getenv('COMPREFACE_URL')
        recognition_url = f"{BASE_URL}/api/v1/recognition/recognize"

        headers = { 'x-api-key': MICROSERVICE_KEY }

        try:
            # Convert the image to bytes
            image_bytes = image_file.read()

            # Create a multipart form data payload
            files = { 'file': ('image.jpg', image_bytes, 'image/jpeg') }

            # Make the API request
            response = requests.post(recognition_url, headers=headers, files=files)
            response.raise_for_status()

            # Parse the response
            result = response.json()
            current_app.logger.info(f"API Clockin - Recognition result: {result}")

            # Check if any faces were detected
            if ('result' in result and len(result['result']) < 0) or ('result' not in result):
                current_app.logger.error(f"API Clockin - No faces detected in the image")
                return jsonify(success=False, error="No faces detected in the image"), 400
            
            # Get the first face with the highest similarity
            faces = result['result']
            if len(faces) < 0:
                current_app.logger.error(f"API Clockin - No faces detected in the image")
                return jsonify(success=False, error="No faces detected in the image"), 400
            
            # Get returned face
            face = faces[0]
            subjects = face.get('subjects', [])

            if len(subjects) < 0:
                current_app.logger.error(f"API Clockin - No matching subjects found")
                return jsonify(success=False, error="No matching subjects found"), 400
            
            # Get the subject with the highest similarity
            subject = max(subjects, key=lambda x: x.get('similarity', 0))
            similarity = subject.get('similarity', 0)
            subject_id = subject.get('subject', '')

            current_app.logger.info(f"API Clockin - Subject ID: {subject_id}, Similarity: {similarity}")

            # Check if the similarity is above the threshold
            if similarity <= 0.85:  # 85% similarity threshold
                current_app.logger.error(f"API Clockin - Face similarity too low: {similarity}")
                return jsonify(success=False, error=f"Face similarity too low: {similarity}"), 400
            
            # Get the employee ID from the subject ID
            employee_id = subject_id

            # Check if the employee is allowed to clock in at this location
            location_name = "Unknown"
            try:
                # Reverse geocode the location
                location_name = Attendance.reverse_geocode(latitude, longitude)
                current_app.logger.info(f"API Clockin - Location name: {location_name}")
            except Exception as e:
                current_app.logger.error(f"API Clockin - Error reverse geocoding: {e}")

            # Record the attendance
            try:
                # Convert the location as string
                location = str(location)
                # Log all parameters for debugging
                current_app.logger.info(f"API Clockin - Parameters: employee_id={employee_id}, location={location}, device_used={device_used}, location_name={location_name}")

                result = Attendance.clockin(db_session, employee_id, location, device_used, location_name)
                current_app.logger.info(f"API Clockin - Attendance recorded: {result}")

                return jsonify(success=True, message=result), 200
            except Exception as e:
                import traceback
                current_app.logger.error(f"API Clockin - Error recording attendance: {e}")
                current_app.logger.error(traceback.format_exc())
                return jsonify(success=False, error=f"Failed to record attendance"), 500
        except requests.exceptions.RequestException as e:
            current_app.logger.error(f"API Clockin - Error making recognition request: {e}")
            return jsonify(success=False, error="Face not rercognized"), 500

@attendance_api_bp.route('/v2/clockout', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr', 'employee'])
def clockout():
    """Clock out an employee using facial recognition."""
    
    jwt_data = get_jwt()
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    current_app.logger.info(f"Clockout - MICROSERVICE_KEY: {MICROSERVICE_KEY}")

    if not MICROSERVICE_KEY:
        error = """
        You are not registered to use this service.
        Please contact <NAME_EMAIL> to be registered.
        """
        current_app.logger.error(f"Clockout - Error: {error}")
        return jsonify(success=False, error=error), 401
    
    company_id = jwt_data.get('company_id')
    database_name = jwt_data.get('database_name')
    employee_id = jwt_data.get('user_id')
    current_app.logger.info(f"Clockout - Company ID from session: {company_id}")

    # Process the uploaded image
    if 'image' not in request.files:
        current_app.logger.error("API Clockout - No image part in the request")
        return jsonify(success=False, error="No image uploaded"), 400

    image_file = request.files['image']
    if image_file.filename == '':
        current_app.logger.error("API Clockout - No image selected")
        return jsonify(success=False, error="No image selected"), 400
    
    # Get location data
    latitude = request.form.get('latitude')
    longitude = request.form.get('longitude')
    location = f"{latitude}, {longitude}"
    current_app.logger.info(f"API Clockout - Location: {location}")

    # Connect to the database
    db_connection = DatabaseConnection()
    with db_connection.get_session(database_name) as db_session:
        # Verify the employee's face
        BASE_URL = os.getenv('COMPREFACE_URL')
        recognition_url = f"{BASE_URL}/api/v1/recognition/recognize"
        headers = { 'x-api-key': MICROSERVICE_KEY }

        try:
            # Convert the image to bytes
            image_bytes = image_file.read()

            # Create a multipart form data payload
            files = { 'file': ('image.jpg', image_bytes, 'image/jpeg') }
            # Make the API request
            response = requests.post(recognition_url, headers=headers, files=files)
            response.raise_for_status()

            # Parse the response
            result = response.json()
            current_app.logger.info(f"API Clockout - Recognition result: {result}")

            # Check if no faces were detected
            if 'result' not in result or ('result' in result and len(result['result']) < 0):
                current_app.logger.error("API Clockout - No faces detected")
                return jsonify(success=False, error="No faces detected"), 400
            
            # Get the first face with the highest similarity
            faces = result['result']
            if len(faces) <= 0:
                current_app.logger.error("API Clockout - No faces detected")
                return jsonify(success=False, error="No faces detected"), 400
            
            face = faces[0]
            subjects = face.get('subjects', [])
            if len(subjects) <= 0:
                current_app.logger.error("API Clockout - No subjects detected")
                return jsonify(success=False, error="No subjects detected"), 400

            # Get the subject with the highest similarity
            subject = max(subjects, key=lambda x: x.get('similarity', 0))
            similarity = subject.get('similarity', 0)
            subject_id = subject.get('subject', '')

            current_app.logger.info(f"API Clockout - Subject ID: {subject_id}, Similarity: {similarity}")
            # Check if the similarity is above the threshold
            if similarity < 0.85:  # 85% similarity threshold
                current_app.logger.error("API Clockout - Face not recognized - Threshold not met")
                return jsonify(success=False, error="Face not recognized"), 400
            
            # Get the employee ID from the subject ID
            employee_id = subject_id
            
            # Check if the employee is allowed to clock out at this location
            location_name = "Unknown"
            try:
                # Reverse geocode the location
                location_name = Attendance.reverse_geocode(latitude, longitude)
                current_app.logger.info(f"API Clockout - Location name: {location_name}")
            except Exception as e:
                current_app.logger.error(f"API Clockout - Error reverse geocoding: {e}")
            
            # Record the attendance
            try:
                # Log all parameters for debugging
                current_app.logger.info(f"API Clockout - Parameters: employee_id={employee_id}, location={location}, location_name={location_name}")

                result = Attendance.clockout(db_session, employee_id, str(location), location_name)
                current_app.logger.info(f"API Clockout - Attendance recorded: {result}")

                if "you have not" in result:
                    return jsonify(success=False, error=result), 401
                return jsonify(success=True, message=result), 200
            except Exception as e:
                import traceback
                current_app.logger.error(f"API Clockout - Error recording attendance: {e}")
                current_app.logger.error(traceback.format_exc())
                return jsonify(success=False, error="Failed to record attendance"), 500
        except Exception as e:
            import traceback
            current_app.logger.error(f"API Clockout - Error verifying face: {e}")
            current_app.logger.error(traceback.format_exc())
            return jsonify(success=False, error="Failed to verify face"), 500
        
@attendance_api_bp.route('/get_leave_records', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def get_leave_records():
    """Get the leave records of all employees from the database."""

    jwt_data = get_jwt()
    # Get the API KEY from CompanyHelpers
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    if MICROSERVICE_KEY is None:
        error = """
        You are not registered to use this service.
        Please contact <NAME_EMAIL> to be registered.
        """
        return jsonify(success=False, error=error), 401

    # Get the database name from the session
    database_name = jwt_data.get('database_name')
    current_app.logger.info(f"Database name: {database_name}")
    if not database_name:
        return jsonify(success=False, error="An error occurred. Try again later or re-login"), 400

    # Create a DatabaseConnection object
    db_connection = DatabaseConnection()
    with db_connection.get_session(database_name) as db_session:
        current_app.logger.info("Getting leave records")

        try:
            current_app.logger.info("Inside try block to get leave records")
            leave_records = Attendance.get_leave_records(db_session)
            current_app.logger.info("Leave records retrieved successfully")

            return jsonify(success=True, data=leave_records, message="Leave records retrieved successfully"), 200
        except Exception as e:
            current_app.logger.error(f"inside except block to get leave records")
            current_app.logger.error(f"Error getting leave records: {e}")
            return jsonify(success=False, error='An error occurred. Please try again later.'), 500

@attendance_api_bp.route('/record_leave_or_off', methods=['POST'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def record_leave_or_off():
    """Record leave or off days for an employee."""
    current_app.logger.info("accessing recoring of leave mannually")

    jwt_data = get_jwt()
    data = request.get_json()
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key() # Get the API KEY from CompanyHelpers
    if MICROSERVICE_KEY is None:
        error = "You are not registered to use this service. Please contact <NAME_EMAIL>"
        return jsonify(succes=False, error=error), 401
    
    # Get the database name from the session
    database_name = jwt_data.get('database_name')
    current_app.logger.info(f"Database name: {database_name}")
    db_connection = DatabaseConnection()

    employee_id = data.get('employee_id')  # Get the employee ID from the form
    work_status = data.get('work_status')
    time_off_begin_date = data.get('time_off_begin_date')
    time_off_end_date = data.get('time_off_end_date')
    remarks = data.get('remarks')

    is_valid, errors = UserInputValidator.validate({
        "employee_id": employee_id,
        "work_status": work_status,
        "time_off_begin_date": time_off_begin_date,
        "time_off_end_date": time_off_end_date,
        "remarks": remarks
    }, 'record_leave_or_off')
    if not is_valid:
        return jsonify(success=False, error=errors), 400
    
    # Connect to the database and record the leave or off days for the employee
    with db_connection.get_session(database_name) as db_session:
        try:
            # Block to give employee leave
            done, result = Attendance.add_work_status(db_session, employee_id, work_status,time_off_begin_date, time_off_end_date)
            if not done:
                return jsonify(success=False, error=result), 400

            message = f"{Auxillary.calculate_days_difference(time_off_begin_date, time_off_end_date)} days recorded successfully for the employee."
            current_app.logger.info(f"Leave or off days recorded: {result}")
            return jsonify(success=True, message=message), 200
        except Exception as e:
            current_app.logger.error(f"Error recording leave or off days: {e}")
            return jsonify(success=False, error="Failed to record leave or off days"), 500

@attendance_api_bp.route('/update_leave_record/<attendance_id>', methods=['PUT'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def update_leave_record(attendance_id):
    """Update a leave record for an employee."""
    # Get the API KEY from CompanyHelpers
    jwt_data = get_jwt()
    data = request.get_json()
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    if MICROSERVICE_KEY is None:
        error = """
        You are not registered to use this service.
        Please contact <NAME_EMAIL> to be registered.
        """
        return jsonify(success=False, error=error), 401

    # Get the database name from the session
    database_name = jwt_data.get('database_name')
    db_connection = DatabaseConnection()

    # Extract data from the request
    time_off_begin_date = data.get('time_off_begin_date')
    time_off_end_date = data.get('time_off_end_date')
    remarks = data.get('remarks')
    work_status = data.get('work_status')

    is_valid, errors = UserInputValidator.validate({
        "time_off_begin_date": time_off_begin_date,
        "time_off_end_date": time_off_end_date,
        "remarks": remarks,
        "work_status": work_status
    }, 'record_leave_or_off')

    if not is_valid:
        return jsonify(success=True, error=errors), 400

    # Connect to the database and update the leave record for the employee
    with db_connection.get_session(database_name) as db_session:
        try:
            result = Attendance.update_leave_record(
                db_session, attendance_id, time_off_begin_date,
                time_off_end_date, remarks, work_status)

            current_app.logger.info(f"Leave record updated: {result}")
            if result is False:
                return jsonify(success=False, error="Failed to update leave record"), 500
            
            current_app.logger.info(f"Leave record updated: {result}")
            return jsonify(success=True, message="Leave record updated successfully."), 200
        except Exception as e:
            current_app.logger.error(f"Error updating leave record: {e}")
            return jsonify(success=False, error="Failed to update leave record"), 500